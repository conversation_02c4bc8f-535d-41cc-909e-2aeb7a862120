Metadata-Version: 2.1
Name: notebook
Version: 6.4.12
Summary: A web-based notebook environment for interactive computing
Home-page: http://jupyter.org
Author: Jupyter Development Team
Author-email: <EMAIL>
License: BSD
Keywords: Interactive,Interpreter,Shell,Web
Platform: Linux
Platform: Mac OS X
Platform: Windows
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: jinja2
Requires-Dist: tornado (>=6.1)
Requires-Dist: pyzmq (>=17)
Requires-Dist: argon2-cffi
Requires-Dist: traitlets (>=4.2.1)
Requires-Dist: jupyter-core (>=4.6.1)
Requires-Dist: jupyter-client (>=5.3.4)
Requires-Dist: ipython-genutils
Requires-Dist: nbformat
Requires-Dist: nbconvert (>=5)
Requires-Dist: nest-asyncio (>=1.5)
Requires-Dist: ipykernel
Requires-Dist: Send2Trash (>=1.8.0)
Requires-Dist: terminado (>=0.8.3)
Requires-Dist: prometheus-client
Provides-Extra: docs
Requires-Dist: sphinx ; extra == 'docs'
Requires-Dist: nbsphinx ; extra == 'docs'
Requires-Dist: sphinxcontrib-github-alt ; extra == 'docs'
Requires-Dist: sphinx-rtd-theme ; extra == 'docs'
Requires-Dist: myst-parser ; extra == 'docs'
Provides-Extra: json-logging
Requires-Dist: json-logging ; extra == 'json-logging'
Provides-Extra: test
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: coverage ; extra == 'test'
Requires-Dist: requests ; extra == 'test'
Requires-Dist: testpath ; extra == 'test'
Requires-Dist: nbval ; extra == 'test'
Requires-Dist: selenium ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'
Requires-Dist: requests-unixsocket ; (sys_platform != "win32") and extra == 'test'


The Jupyter Notebook is a web application that allows you to create and
share documents that contain live code, equations, visualizations, and
explanatory text. The Notebook has support for multiple programming
languages, sharing, and interactive widgets.

Read `the documentation <https://jupyter-notebook.readthedocs.io>`_
for more information.
    
