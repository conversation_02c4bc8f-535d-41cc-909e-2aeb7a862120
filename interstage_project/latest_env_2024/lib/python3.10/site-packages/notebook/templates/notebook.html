{% extends "page.html" %}

{% block favicon %}<link id="favicon" rel="shortcut icon" type="image/x-icon" href="{{static_url("base/images/favicon-notebook.ico") }}">{% endblock %}

{% block stylesheet %}

{% if mathjax_url %}
<script type="text/javascript" src="{{mathjax_url}}?config={{mathjax_config}}&delayStartupUntil=configured" charset="utf-8"></script>
{% endif %}
<script type="text/javascript">
// MathJax disabled, set as null to distinguish from *missing* MathJax,
// where it will be undefined, and should prompt a dialog later.
window.mathjax_url = "{{mathjax_url}}";
</script>

<link rel="stylesheet" href="{{ static_url("components/bootstrap-tour/build/css/bootstrap-tour.min.css") }}" type="text/css" />
<link rel="stylesheet" href="{{ static_url("components/codemirror/lib/codemirror.css") }}">

{{super()}}

<link rel="stylesheet" href="{{ static_url("notebook/css/override.css") }}" type="text/css" />
<link rel="stylesheet" href=""  id='kernel-css'                             type="text/css" />

{% endblock %}

{% block bodyclasses %}notebook_app {{super()}}{% endblock %}

{% block params %}

{{super()}}
data-base-url="{{base_url | urlencode}}"
data-ws-url="{{ws_url | urlencode}}"
data-notebook-name="{{notebook_name | urlencode}}"
data-notebook-path="{{notebook_path | urlencode}}"
{% endblock %}


{% block headercontainer %}


<span id="save_widget" class="save_widget">
    <span id="notebook_name" class="filename"></span>
    <span class="checkpoint_status"></span>
    <span class="autosave_status"></span>
</span>

{{super()}}

<span id="kernel_logo_widget">
  {% block kernel_logo_widget %}
  <img class="current_kernel_logo" alt="Current Kernel Logo" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"/>
  {% endblock %}
</span>

{% endblock headercontainer %}

{% block header %}
<div id="menubar-container" class="container">
<div id="menubar">
    <div id="menus" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <button type="button" class="btn btn-default navbar-btn navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
              <i class="fa fa-bars"></i>
              <span class="navbar-text">{% trans %}Menu{% endtrans %}</span>
            </button>
            <p id="kernel_indicator" class="navbar-text indicator_area">
              <span class="kernel_indicator_name">{% trans %}Kernel{% endtrans %}</span>
              <i id="kernel_indicator_icon"></i>
            </p>
            <i id="readonly-indicator" class="navbar-text" title='{% trans %}This notebook is read-only{% endtrans %}'>
                <span class="fa-stack">
                    <i class="fa fa-save fa-stack-1x"></i>
                    <i class="fa fa-ban fa-stack-2x text-danger"></i>
                </span>
            </i>
            <i id="modal_indicator" class="navbar-text"></i>
            <span id="notification_area"></span>
            <div class="navbar-collapse collapse">
              <ul class="nav navbar-nav">
                <li class="dropdown"><a href="#" class="dropdown-toggle" id="filelink" data-toggle="dropdown" aria-haspopup="true" aria-controls="file_menu">{% trans %}File{% endtrans %}</a>
                    <ul id="file_menu" class="dropdown-menu" role="menu" aria-labelledby="filelink">
                        <li id="new_notebook" class="menu_focus_highlight dropdown dropdown-submenu" role="none">
                            <a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}New Notebook{% endtrans %}<span class="sr-only">Dropdown</span></a>
                            <ul class="dropdown-menu" id="menu-new-notebook-submenu" role="menu">
                            </ul>
                        </li>
                        <li id="open_notebook" role="none"
                            title="{% trans %}Opens a new window with the Dashboard view{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Open...{% endtrans %}</a></li>
                        <!-- <hr/> -->
                        <li class="divider" role="none"></li>
                        <li id="copy_notebook" role="none"
                            title="{% trans %}Open a copy of this notebook's contents and start a new kernel{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Make a Copy...{% endtrans %}</a></li>
                        <li id="save_notebook_as" role="none"
                            title="{% trans %}Save a copy of the notebook's contents and start a new kernel{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Save as...{% endtrans %}</a></li>
                        <li id="rename_notebook" role="none"><a href="#" role="menuitem">{% trans %}Rename...{% endtrans %}</a></li>
                        <li id="save_checkpoint" role="none"><a href="#" role="menuitem">{% trans %}Save and Checkpoint{% endtrans %}</a></li>
                        <!-- <hr/> -->
                        <li class="divider" role="none"></li>
                        <li id="restore_checkpoint" class="menu_focus_highlight dropdown-submenu" role="none"><a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}Revert to Checkpoint{% endtrans %}<span class="sr-only">Dropdown</span></a>
                          <ul class="dropdown-menu">
                            <li><a href="#"></a></li>
                            <li><a href="#"></a></li>
                            <li><a href="#"></a></li>
                            <li><a href="#"></a></li>
                            <li><a href="#"></a></li>
                          </ul>
                        </li>
                        <li class="divider" role="none"></li>
                        <li id="print_preview" role="none"><a href="#" role="menuitem">{% trans %}Print Preview{% endtrans %}</a></li>
                        <li class="dropdown-submenu menu_focus_highlight" role="none"><a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}Download as{% endtrans %}<span class="sr-only">Dropdown</span></a>
                            <ul id="download_menu" class="dropdown-menu">
                                {% for exporter in get_frontend_exporters() %}
                                <li id="download_{{ exporter.name }}">
                                    <a href="#">{{ exporter.display }}</a>
                                </li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li class="dropdown-submenu hidden" role="none"><a href="#" role="menuitem">{% trans %}Deploy as{% endtrans %}</a>
                            <ul id="deploy_menu" class="dropdown-menu"></ul>
                        </li>
                        <li class="divider" role="none"></li>
                        <li id="trust_notebook" role="none"
                            title="{% trans %}Trust the output of this notebook{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Trust Notebook{% endtrans %}</a></li>
                        <li class="divider" role="none"></li>
                        <li id="close_and_halt" role="none"
                            title="{% trans %}Shutdown this notebook's kernel, and close this window{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Close and Halt{% endtrans %}</a></li>
                    </ul>
                </li>

                <li class="dropdown"><a href="#" class="dropdown-toggle" id="editlink" data-toggle="dropdown" aria-haspopup="true" aria-controls="edit_menu">{% trans %}Edit{% endtrans %}</a>
                    <ul id="edit_menu" class="dropdown-menu" role="menu" aria-labelledby="editlink">
                        <li id="cut_cell" role="none"><a href="#" role="menuitem">{% trans %}Cut Cells{% endtrans %}</a></li>
                        <li id="copy_cell" role="none"><a href="#" role="menuitem">{% trans %}Copy Cells{% endtrans %}</a></li>
                        <li id="paste_cell_above" class="disabled" role="none"><a href="#" role="menuitem" aria-disabled="true">{% trans %}Paste Cells Above{% endtrans %}</a></li>
                        <li id="paste_cell_below" class="disabled" role="none"><a href="#" role="menuitem" aria-disabled="true">{% trans %}Paste Cells Below{% endtrans %}</a></li>
                        <li id="paste_cell_replace" class="disabled" role="none"><a href="#" role="menuitem" aria-disabled="true">{% trans %}Paste Cells &amp; Replace{% endtrans %}</a></li>
                        <li id="delete_cell" role="none"><a href="#" role="menuitem">{% trans %}Delete Cells{% endtrans %}</a></li>
                        <li id="undelete_cell" class="disabled" role="none"><a href="#" role="menuitem" aria-disabled="true">{% trans %}Undo Delete Cells{% endtrans %}</a></li>
                        <li class="divider" role="none"></li>
                        <li id="split_cell" role="none"><a href="#" role="menuitem">{% trans %}Split Cell{% endtrans %}</a></li>
                        <li id="merge_cell_above" role="none"><a href="#" role="menuitem">{% trans %}Merge Cell Above{% endtrans %}</a></li>
                        <li id="merge_cell_below" role="none"><a href="#" role="menuitem">{% trans %}Merge Cell Below{% endtrans %}</a></li>
                        <li class="divider" role="none"></li>
                        <li id="move_cell_up" role="none"><a href="#" role="menuitem">{% trans %}Move Cell Up{% endtrans %}</a></li>
                        <li id="move_cell_down" role="none"><a href="#" role="menuitem">{% trans %}Move Cell Down{% endtrans %}</a></li>
                        <li class="divider" role="none"></li>
                        <li id="edit_nb_metadata" role="none"><a href="#" role="menuitem">{% trans %}Edit Notebook Metadata{% endtrans %}</a></li>
                        <li class="divider" role="none"></li>
                        <li id="find_and_replace" role="none"><a href="#" role="menuitem"> {% trans %}Find and Replace{% endtrans %} </a></li>
                        <li class="divider" role="none"></li>
                        <li id="cut_cell_attachments" role="none"><a href="#" role="menuitem">{% trans %}Cut Cell Attachments{% endtrans %}</a></li>
                        <li id="copy_cell_attachments" role="none"><a href="#" role="menuitem">{% trans %}Copy Cell Attachments{% endtrans %}</a></li>
                        <li id="paste_cell_attachments"  class="disabled" role="none"><a href="#" role="menuitem" aria-disabled="true">{% trans %}Paste Cell Attachments{% endtrans %}</a></li>
                        <li class="divider" role="none"></li>
                        <li id="insert_image" class="disabled" role="none"><a href="#" role="menuitem" aria-disabled="true">  {% trans %}Insert Image{% endtrans %} </a></li>
                    </ul>
                </li>
                <li class="dropdown"><a href="#" class="dropdown-toggle" id="viewlink" data-toggle="dropdown" aria-haspopup="true" aria-controls="view_menu">{% trans %}View{% endtrans %}</a>
                    <ul id="view_menu" class="dropdown-menu" role="menu" aria-labelledby="viewlink">
                        <li id="toggle_header" role="none"
                            title="{% trans %}Show/Hide the logo and notebook title (above menu bar){% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Toggle Header{% endtrans %}</a>
                        </li>
                        <li id="toggle_toolbar" role="none"
                            title="{% trans %}Show/Hide the action icons (below menu bar){% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Toggle Toolbar{% endtrans %}</a>
                        </li>
                        <li id="toggle_line_numbers" role="none"
                            title="{% trans %}Show/Hide line numbers in cells{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Toggle Line Numbers{% endtrans %}</a>
                        </li>
                        <li id="menu-cell-toolbar" class="menu_focus_highlight dropdown-submenu" role="none">
                            <a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}Cell Toolbar{% endtrans %}</a>
                            <ul class="dropdown-menu" id="menu-cell-toolbar-submenu"></ul>
                        </li>
                    </ul>
                </li>
                <li class="dropdown"><a href="#" class="dropdown-toggle" id="insertlink" data-toggle="dropdown" aria-haspopup="true" aria-controls="insert_menu">{% trans %}Insert{% endtrans %}</a>
                    <ul id="insert_menu" class="dropdown-menu" role="menu" aria-labelledby="insertlink">
                        <li id="insert_cell_above" role="none"
                            title="{% trans %}Insert an empty Code cell above the currently active cell{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Insert Cell Above{% endtrans %}</a></li>
                        <li id="insert_cell_below" role="none"
                            title="{% trans %}Insert an empty Code cell below the currently active cell{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Insert Cell Below{% endtrans %}</a></li>
                    </ul>
                </li>
                <li class="dropdown"><a href="#" class="dropdown-toggle" id="celllink" data-toggle="dropdown" aria-haspopup="true" aria-controls="cell_menu">{% trans %}Cell{% endtrans %}</a>
                    <ul id="cell_menu" class="dropdown-menu" role="menu" aria-labelledby="celllink">
                        <li id="run_cell" role="none" title="{% trans %}Run this cell, and move cursor to the next one{% endtrans %}">
                            <a role="menuitem" href="#">{% trans %}Run Cells{% endtrans %}</a></li>
                        <li id="run_cell_select_below" role="none" title="{% trans %}Run this cell, select below{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Run Cells and Select Below{% endtrans %}</a></li>
                        <li id="run_cell_insert_below" role="none" title="{% trans %}Run this cell, insert below{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Run Cells and Insert Below{% endtrans %}</a></li>
                        <li id="run_all_cells" role="none" title="{% trans %}Run all cells in the notebook{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Run All{% endtrans %}</a></li>
                        <li id="run_all_cells_above" role="none" title="{% trans %}Run all cells above (but not including) this cell{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Run All Above{% endtrans %}</a></li>
                        <li id="run_all_cells_below" role="none" title="{% trans %}Run this cell and all cells below it{% endtrans %}">
                            <a href="#" role="menuitem">{% trans %}Run All Below{% endtrans %}</a></li>
                        <li class="divider" role="none"></li>
                        <li id="change_cell_type" class="menu_focus_highlight dropdown-submenu" role="none"
                            title="{% trans %}All cells in the notebook have a cell type. By default, new cells are created as 'Code' cells{% endtrans %}">
                            <a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}Cell Type{% endtrans %}</a>
                            <ul class="dropdown-menu" role="menu">
                              <li id="to_code" role="none"
                                  title="{% trans %}Contents will be sent to the kernel for execution, and output will display in the footer of cell{% endtrans %}">
                                  <a href="#">Code</a></li>
                              <li id="to_markdown"
                                  title="{% trans %}Contents will be rendered as HTML and serve as explanatory text{% endtrans %}">
                                  <a href="#">{% trans %}Markdown{% endtrans %}</a></li>
                              <li id="to_raw"
                                  title="{% trans %}Contents will pass through nbconvert unmodified{% endtrans %}">
                                  <a href="#">{% trans %}Raw NBConvert{% endtrans %}</a></li>
                            </ul>
                        </li>
                        <li class="divider" role="none"></li>
                        <li id="current_outputs" class="menu_focus_highlight dropdown-submenu" role="none"><a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}Current Outputs{% endtrans %}</a>
                            <ul class="dropdown-menu" role="menu">
                                <li id="toggle_current_output" role="none"
                                    title="{% trans %}Hide/Show the output of the current cell{% endtrans %}">
                                    <a href="#">{% trans %}Toggle{% endtrans %}</a>
                                </li>
                                <li id="toggle_current_output_scroll"
                                    title="{% trans %}Scroll the output of the current cell{% endtrans %}">
                                    <a href="#">{% trans %}Toggle Scrolling{% endtrans %}</a>
                                </li>
                                <li id="clear_current_output"
                                    title="{% trans %}Clear the output of the current cell{% endtrans %}">
                                    <a href="#">{% trans %}Clear{% endtrans %}</a>
                                </li>
                            </ul>
                        </li>
                        <li id="all_outputs" class="menu_focus_highlight dropdown-submenu" role="none"><a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}All Output{% endtrans %}</a>
                            <ul class="dropdown-menu" role="menu">
                                <li id="toggle_all_output" role="none"
                                    title="{% trans %}Hide/Show the output of all cells{% endtrans %}">
                                    <a href="#">{% trans %}Toggle{% endtrans %}</a>
                                </li>
                                <li id="toggle_all_output_scroll"
                                    title="{% trans %}Scroll the output of all cells{% endtrans %}">
                                    <a href="#">{% trans %}Toggle Scrolling{% endtrans %}</a>
                                </li>
                                <li id="clear_all_output"
                                    title="{% trans %}Clear the output of all cells{% endtrans %}">
                                    <a href="#">{% trans %}Clear{% endtrans %}</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown" id="kernellink">{% trans %}Kernel{% endtrans %}</a>
                    <ul id="kernel_menu" class="dropdown-menu" aria-labelledby="kernellink">
                        <li id="int_kernel"
                            title="{% trans %}Send Keyboard Interrupt (CTRL-C) to the Kernel{% endtrans %}">
                            <a href="#">{% trans %}Interrupt{% endtrans %}</a>
                        </li>
                        <li id="restart_kernel"
                            title="{% trans %}Restart the Kernel{% endtrans %}">
                            <a href="#">{% trans %}Restart{% endtrans %}</a>
                        </li>
                        <li id="restart_clear_output"
                            title="{% trans %}Restart the Kernel and clear all output{% endtrans %}">
                            <a href="#">{% trans %}Restart &amp; Clear Output{% endtrans %}</a>
                        </li>
                        <li id="restart_run_all"
                            title="{% trans %}Restart the Kernel and re-run the notebook{% endtrans %}">
                            <a href="#">{% trans %}Restart &amp; Run All{% endtrans %}</a>
                        </li>
                        <li id="reconnect_kernel"
                            title="{% trans %}Reconnect to the Kernel{% endtrans %}">
                            <a href="#">{% trans %}Reconnect{% endtrans %}</a>
                        </li>
                        <li id="shutdown_kernel"
                            title="Shutdown the Kernel">
                            <a href="#">{% trans %}Shutdown{% endtrans %}</a>
                        </li>
                        <li class="divider" role="none"></li>
                        <li id="menu-change-kernel" class="menu_focus_highlight dropdown-submenu" role="menuitem">
                            <a href="#" role="menuitem" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown">{% trans %}Change kernel{% endtrans %}</a>
                            <ul class="dropdown-menu" id="menu-change-kernel-submenu"></ul>
                        </li>
                    </ul>
                </li>
                <li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown">{% trans %}Help{% endtrans %}</a>
                    <ul  id="help_menu" class="dropdown-menu">
                        {% block help %}
                        <li id="notebook_tour" title="{% trans %}A quick tour of the notebook user interface{% endtrans %}"><a href="#">{% trans %}User Interface Tour{% endtrans %}</a></li>
                        <li id="keyboard_shortcuts" title="{% trans %}Opens a tooltip with all keyboard shortcuts{% endtrans %}"><a href="#">{% trans %}Keyboard Shortcuts{% endtrans %}</a></li>
                        <li id="edit_keyboard_shortcuts" title="{% trans %}Opens a dialog allowing you to edit Keyboard shortcuts{% endtrans %}"><a href="#">{% trans %}Edit Keyboard Shortcuts{% endtrans %}</a></li>
                        <li class="divider"></li>
                        {% set
                            sections = (
                                (
                                    ("http://nbviewer.jupyter.org/github/ipython/ipython/blob/3.x/examples/Notebook/Index.ipynb", _("Notebook Help"), True),
                                    ("https://help.github.com/articles/markdown-basics/",_("Markdown"),True),
                                ),
                            )
                        %}

						{% set openmsg = _("Opens in a new window") %}
                        {% for helplinks in sections %}
                            {% for link in helplinks %}
                                <li><a rel="noreferrer" href="{{link[0]}}" target="{{'_blank' if link[2]}}" title="{{openmsg if link[2]}}">
                                {% if link[2] %}
                                    <i class="fa fa-external-link menu-icon pull-right"></i>
                                {% endif %}

                                {{link[1]}}
                                </a></li>
                            {% endfor %}
                            {% if not loop.last %}
                                <li class="divider"></li>
                            {% endif %}
                        {% endfor %}
                        <li class="divider"></li>
                        <li title="{% trans %}About Jupyter Notebook{% endtrans %}"><a id="notebook_about" href="#">{% trans %}About{% endtrans %}</a></li>
                        {% endblock %}
                    </ul>
                </li>
              </ul>
            </div>
        </div>
    </div>
</div>

<div id="maintoolbar" class="navbar">
  <div class="toolbar-inner navbar-inner navbar-nobg">
    <div id="maintoolbar-container" class="container"></div>
  </div>
</div>
</div>

<div class="lower-header-bar"></div>
{% endblock header %}

{% block site %}

<div id="ipython-main-app">
    <div id="notebook_panel">
        <div id="notebook"></div>
        <div id='tooltip' class='ipython_tooltip' style='display:none'></div>
    </div>
</div>


{% endblock %}

{% block after_site %}

<div id="pager">
    <div id="pager-contents">
        <div id="pager-container" class="container"></div>
    </div>
    <div id='pager-button-area'></div>
</div>

{% endblock %}

{% block script %}
{{super()}}
<script type="text/javascript">
    sys_info = {{sys_info|safe}};
</script>

<script src="{{ static_url("components/text-encoding/lib/encoding.js") }}" charset="utf-8"></script>

<script src="{{ static_url("notebook/js/main.min.js") }}" type="text/javascript" charset="utf-8"></script>

{% endblock %}
