# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-07-08 21:52-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

#: notebook/notebookapp.py:53
msgid "The Jupyter Notebook requires tornado >= 4.0"
msgstr ""

#: notebook/notebookapp.py:57
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have < 1.1.0"
msgstr ""

#: notebook/notebookapp.py:59
#, python-format
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have %s"
msgstr ""

#: notebook/notebookapp.py:209
msgid "The `ignore_minified_js` flag is deprecated and no longer works."
msgstr ""

#: notebook/notebookapp.py:210
#, python-format
msgid "Alternatively use `%s` when working on the notebook's Javascript and LESS"
msgstr ""

#: notebook/notebookapp.py:211
msgid "The `ignore_minified_js` flag is deprecated and will be removed in Notebook 6.0"
msgstr ""

#: notebook/notebookapp.py:389
msgid "List currently running notebook servers."
msgstr ""

#: notebook/notebookapp.py:393
msgid "Produce machine-readable JSON output."
msgstr ""

#: notebook/notebookapp.py:397
msgid "If True, each line of output will be a JSON object with the details from the server info file."
msgstr ""

#: notebook/notebookapp.py:402
msgid "Currently running servers:"
msgstr ""

#: notebook/notebookapp.py:419
msgid "Don't open the notebook in a browser after startup."
msgstr ""

#: notebook/notebookapp.py:423
msgid "DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib."
msgstr ""

#: notebook/notebookapp.py:439
msgid "Allow the notebook to be run from root user."
msgstr ""

#: notebook/notebookapp.py:470
msgid ""
"The Jupyter HTML Notebook.\n"
"    \n"
"    This launches a Tornado based HTML Notebook Server that serves up an HTML5/Javascript Notebook client."
msgstr ""

#: notebook/notebookapp.py:509
msgid "Deprecated: Use minified JS file or not, mainly use during dev to avoid JS recompilation"
msgstr ""

#: notebook/notebookapp.py:540
msgid "Set the Access-Control-Allow-Credentials: true header"
msgstr ""

#: notebook/notebookapp.py:544
msgid "Whether to allow the user to run the notebook as root."
msgstr ""

#: notebook/notebookapp.py:548
msgid "The default URL to redirect to from `/`"
msgstr ""

#: notebook/notebookapp.py:552
msgid "The IP address the notebook server will listen on."
msgstr ""

#: notebook/notebookapp.py:565
#, python-format
msgid ""
"Cannot bind to localhost, using 127.0.0.1 as default ip\n"
"%s"
msgstr ""

#: notebook/notebookapp.py:579
msgid "The port the notebook server will listen on."
msgstr ""

#: notebook/notebookapp.py:583
msgid "The number of additional ports to try if the specified port is not available."
msgstr ""

#: notebook/notebookapp.py:587
msgid "The full path to an SSL/TLS certificate file."
msgstr ""

#: notebook/notebookapp.py:591
msgid "The full path to a private key file for usage with SSL/TLS."
msgstr ""

#: notebook/notebookapp.py:595
msgid "The full path to a certificate authority certificate for SSL/TLS client authentication."
msgstr ""

#: notebook/notebookapp.py:599
msgid "The file where the cookie secret is stored."
msgstr ""

#: notebook/notebookapp.py:628
#, python-format
msgid "Writing notebook server cookie secret to %s"
msgstr ""

#: notebook/notebookapp.py:635
#, python-format
msgid "Could not set permissions on %s"
msgstr ""

#: notebook/notebookapp.py:640
msgid ""
"Token used for authenticating first-time connections to the server.\n"
"\n"
"        When no password is enabled,\n"
"        the default is to generate a new, random token.\n"
"\n"
"        Setting to an empty string disables authentication altogether, which is NOT RECOMMENDED.\n"
"        "
msgstr ""

#: notebook/notebookapp.py:650
msgid ""
"One-time token used for opening a browser.\n"
"        Once used, this token cannot be used again.\n"
"        "
msgstr ""

#: notebook/notebookapp.py:726
msgid ""
"Specify Where to open the notebook on startup. This is the\n"
"        `new` argument passed to the standard library method `webbrowser.open`.\n"
"        The behaviour is not guaranteed, but depends on browser support. Valid\n"
"        values are:\n"
"            2 opens a new tab,\n"
"            1 opens a new window,\n"
"            0 opens in an existing window.\n"
"        See the `webbrowser.open` documentation for details.\n"
"        "
msgstr ""

#: notebook/notebookapp.py:737
msgid "DEPRECATED, use tornado_settings"
msgstr ""

#: notebook/notebookapp.py:742
msgid ""
"\n"
"    webapp_settings is deprecated, use tornado_settings.\n"
msgstr ""

#: notebook/notebookapp.py:746
msgid "Supply overrides for the tornado.web.Application that the Jupyter notebook uses."
msgstr ""

#: notebook/notebookapp.py:750
msgid ""
"\n"
"        Set the tornado compression options for websocket connections.\n"
"\n"
"        This value will be returned from :meth:`WebSocketHandler.get_compression_options`.\n"
"        None (default) will disable compression.\n"
"        A dict (even an empty one) will enable compression.\n"
"\n"
"        See the tornado docs for WebSocketHandler.get_compression_options for details.\n"
"        "
msgstr ""

#: notebook/notebookapp.py:761
msgid "Supply overrides for terminado. Currently only supports \"shell_command\"."
msgstr ""

#: notebook/notebookapp.py:764
msgid "Extra keyword arguments to pass to `set_secure_cookie`. See tornado's set_secure_cookie docs for details."
msgstr ""

#: notebook/notebookapp.py:768
msgid ""
"Supply SSL options for the tornado HTTPServer.\n"
"            See the tornado docs for details."
msgstr ""

#: notebook/notebookapp.py:772
msgid "Supply extra arguments that will be passed to Jinja environment."
msgstr ""

#: notebook/notebookapp.py:776
msgid "Extra variables to supply to jinja templates when rendering."
msgstr ""

#: notebook/notebookapp.py:812
msgid "DEPRECATED use base_url"
msgstr ""

#: notebook/notebookapp.py:816
msgid "base_project_url is deprecated, use base_url"
msgstr ""

#: notebook/notebookapp.py:832
msgid "Path to search for custom.js, css"
msgstr ""

#: notebook/notebookapp.py:844
msgid ""
"Extra paths to search for serving jinja templates.\n"
"\n"
"        Can be used to override templates from notebook.templates."
msgstr ""

#: notebook/notebookapp.py:855
msgid "extra paths to look for Javascript notebook extensions"
msgstr ""

#: notebook/notebookapp.py:900
#, python-format
msgid "Using MathJax: %s"
msgstr ""

#: notebook/notebookapp.py:903
msgid "The MathJax.js configuration file that is to be used."
msgstr ""

#: notebook/notebookapp.py:908
#, python-format
msgid "Using MathJax configuration file: %s"
msgstr ""

#: notebook/notebookapp.py:914
msgid "The notebook manager class to use."
msgstr ""

#: notebook/notebookapp.py:920
msgid "The kernel manager class to use."
msgstr ""

#: notebook/notebookapp.py:926
msgid "The session manager class to use."
msgstr ""

#: notebook/notebookapp.py:932
msgid "The config manager class to use"
msgstr ""

#: notebook/notebookapp.py:953
msgid "The login handler class to use."
msgstr ""

#: notebook/notebookapp.py:960
msgid "The logout handler class to use."
msgstr ""

#: notebook/notebookapp.py:964
msgid "Whether to trust or not X-Scheme/X-Forwarded-Proto and X-Real-Ip/X-Forwarded-For headerssent by the upstream reverse proxy. Necessary if the proxy handles SSL"
msgstr ""

#: notebook/notebookapp.py:976
msgid ""
"\n"
"        DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib.\n"
"        "
msgstr ""

#: notebook/notebookapp.py:988
msgid "Support for specifying --pylab on the command line has been removed."
msgstr ""

#: notebook/notebookapp.py:990
msgid "Please use `%pylab{0}` or `%matplotlib{0}` in the notebook itself."
msgstr ""

#: notebook/notebookapp.py:995
msgid "The directory to use for notebooks and kernels."
msgstr ""

#: notebook/notebookapp.py:1018
#, python-format
msgid "No such notebook dir: '%r'"
msgstr ""

#: notebook/notebookapp.py:1031
msgid "DEPRECATED use the nbserver_extensions dict instead"
msgstr ""

#: notebook/notebookapp.py:1036
msgid "server_extensions is deprecated, use nbserver_extensions"
msgstr ""

#: notebook/notebookapp.py:1040
msgid "Dict of Python modules to load as notebook server extensions.Entry values can be used to enable and disable the loading ofthe extensions. The extensions will be loaded in alphabetical order."
msgstr ""

#: notebook/notebookapp.py:1049
msgid "Reraise exceptions encountered loading server extensions?"
msgstr ""

#: notebook/notebookapp.py:1052
msgid ""
"(msgs/sec)\n"
"        Maximum rate at which messages can be sent on iopub before they are\n"
"        limited."
msgstr ""

#: notebook/notebookapp.py:1056
msgid ""
"(bytes/sec)\n"
"        Maximum rate at which stream output can be sent on iopub before they are\n"
"        limited."
msgstr ""

#: notebook/notebookapp.py:1060
msgid ""
"(sec) Time window used to \n"
"        check the message and data rate limits."
msgstr ""

#: notebook/notebookapp.py:1071
#, python-format
msgid "No such file or directory: %s"
msgstr ""

#: notebook/notebookapp.py:1141
msgid "Notebook servers are configured to only be run with a password."
msgstr ""

#: notebook/notebookapp.py:1142
msgid "Hint: run the following command to set a password"
msgstr ""

#: notebook/notebookapp.py:1143
msgid "\t$ python -m notebook.auth password"
msgstr ""

#: notebook/notebookapp.py:1181
#, python-format
msgid "The port %i is already in use, trying another port."
msgstr ""

#: notebook/notebookapp.py:1184
#, python-format
msgid "Permission to listen on port %i denied"
msgstr ""

#: notebook/notebookapp.py:1193
msgid "ERROR: the notebook server could not be started because no available port could be found."
msgstr ""

#: notebook/notebookapp.py:1199
msgid "[all ip addresses on your system]"
msgstr ""

#: notebook/notebookapp.py:1223
#, python-format
msgid "Terminals not available (error was %s)"
msgstr ""

#: notebook/notebookapp.py:1259
msgid "interrupted"
msgstr ""

#: notebook/notebookapp.py:1261
msgid "y"
msgstr ""

#: notebook/notebookapp.py:1262
msgid "n"
msgstr ""

#: notebook/notebookapp.py:1263
#, python-format
msgid "Shutdown this notebook server (%s/[%s])? "
msgstr ""

#: notebook/notebookapp.py:1269
msgid "Shutdown confirmed"
msgstr ""

#: notebook/notebookapp.py:1273
msgid "No answer for 5s:"
msgstr ""

#: notebook/notebookapp.py:1274
msgid "resuming operation..."
msgstr ""

#: notebook/notebookapp.py:1282
#, python-format
msgid "received signal %s, stopping"
msgstr ""

#: notebook/notebookapp.py:1338
#, python-format
msgid "Error loading server extension %s"
msgstr ""

#: notebook/notebookapp.py:1369
#, python-format
msgid "Shutting down %d kernels"
msgstr ""

#: notebook/notebookapp.py:1375
#, python-format
msgid "%d active kernel"
msgid_plural "%d active kernels"
msgstr[0] ""
msgstr[1] ""

#: notebook/notebookapp.py:1379
#, python-format
msgid ""
"The Jupyter Notebook is running at:\n"
"\r"
"%s"
msgstr ""

#: notebook/notebookapp.py:1426
msgid "Running as root is not recommended. Use --allow-root to bypass."
msgstr ""

#: notebook/notebookapp.py:1432
msgid "Use Control-C to stop this server and shut down all kernels (twice to skip confirmation)."
msgstr ""

#: notebook/notebookapp.py:1434
msgid "Welcome to Project Jupyter! Explore the various tools available and their corresponding documentation. If you are interested in contributing to the platform, please visit the communityresources section at http://jupyter.org/community.html."
msgstr ""

#: notebook/notebookapp.py:1445
#, python-format
msgid "No web browser found: %s."
msgstr ""

#: notebook/notebookapp.py:1450
#, python-format
msgid "%s does not exist"
msgstr ""

#: notebook/notebookapp.py:1484
msgid "Interrupted..."
msgstr ""

#: notebook/services/contents/filemanager.py:506
#, python-format
msgid "Serving notebooks from local directory: %s"
msgstr ""

#: notebook/services/contents/manager.py:68
msgid "Untitled"
msgstr ""

