{"domain": "nbjs", "locale_data": {"nbjs": {"": {"domain": "nbjs", "plural_forms": "nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);", "lang": "ru_RU"}, "Manually edit the JSON below to manipulate the metadata for this cell.": ["Отредактируйте JSON ниже вручную для управления метаданными для этой ячейки."], "Manually edit the JSON below to manipulate the metadata for this notebook.": ["Отредактируйте JSON ниже вручную для управления метаданными для этого блокнота."], " We recommend putting custom metadata attributes in an appropriately named substructure, so they don't conflict with those of others.": [" Мы рекомендуем поместить пользовательские атрибуты метаданных в подструктуру с соответствующим именем, чтобы они не конфликтовали с атрибутами других объектов."], "Edit the metadata": ["Редактировать метаданные"], "Edit Notebook Metadata": ["Редактировать метаданные блокнота"], "Edit Cell Metadata": ["Редактировать метаданные ячейки"], "Cancel": ["Отмена"], "Edit": ["Редактировать"], "OK": ["ОК"], "Apply": ["Применить"], "WARNING: Could not save invalid JSON.": ["ПРЕДУПРЕЖДЕНИЕ: не удалось сохранить недопустимый JSON."], "There are no attachments for this cell.": ["Для этой ячейки нет никаких вложений."], "Current cell attachments": ["Вложения текущей ячейки"], "Attachments": ["Вложения"], "Restore": ["Восстановить"], "Delete": ["Удалить"], "Edit attachments": ["Редактировать вложения"], "Edit Notebook Attachments": ["Редактировать вложения блокнотов"], "Edit Cell Attachments": ["Редактировать вложения ячейки"], "Select a file to insert.": ["Выберите файл для вставки."], "Select a file": ["Выберите файл"], "You are using Jupyter notebook.": ["Вы используете блокнот Jupyter."], "The version of the notebook server is: ": ["Версия сервера блокнотов: "], "The server is running on this version of Python:": ["Сервер работает на этой версии Python:"], "Waiting for kernel to be available...": ["Ожидание доступности ядра..."], "Server Information:": ["Информация сервера:"], "Current Kernel Information:": ["Информация о текущем ядре:"], "Could not access sys_info variable for version information.": ["Невозможно получить доступ к переменной sys_info для получения информации о версии."], "Cannot find sys_info!": ["Невозможно найти sys_info!"], "About Jupyter Notebook": ["О блокноте Jupyter"], "unable to contact kernel": ["невозможно связаться с ядром"], "toggle rtl layout": ["переключить макет rtl"], "Toggle the screen directionality between left-to-right and right-to-left": ["Переключение направления экрана между слева направо и справа налево"], "edit command mode keyboard shortcuts": ["редактировать сочетания клавиш командного режима"], "Open a dialog to edit the command mode keyboard shortcuts": ["Откройте диалоговое окно для редактирования сочетаний клавиш командного режима"], "restart kernel": ["перезапуск ядра"], "restart the kernel (no confirmation dialog)": ["перезапуск ядра (диалог подтверждения отсутствует)"], "confirm restart kernel": ["подтвердить перезапуск ядра"], "restart the kernel (with dialog)": ["перезапуск ядра (с диалоговым окном)"], "restart kernel and run all cells": ["перезапуск ядра и запуск всех ячеек"], "restart the kernel, then re-run the whole notebook (no confirmation dialog)": ["перезапустить ядро, а затем перезапустить весь блокнот (диалог подтверждения отсутствует)"], "confirm restart kernel and run all cells": ["подтвердить перезапуск ядра и запуск всех ячеек"], "restart the kernel, then re-run the whole notebook (with dialog)": ["перезапустить ядро, а затем перезапустить весь блокнот (с диалогом подтверждения)"], "restart kernel and clear output": ["перезапуск ядра и очистка вывода"], "restart the kernel and clear all output (no confirmation dialog)": ["перезапустить ядро и очистить все выходные данные (диалог подтверждения отсутствует)"], "confirm restart kernel and clear output": ["подтвердить перезапуск ядра и очистку вывода"], "restart the kernel and clear all output (with dialog)": ["перезапустить ядро и очистить все выходные данные (с диалогом подтверждения)"], "interrupt the kernel": ["прервать работу ядра"], "run cell and select next": ["запустить ячейку и выбрать следующую"], "run cell, select below": ["запустить ячейку, выбрать ниже"], "run selected cells": ["запустить выбранные ячейки"], "run cell and insert below": ["запустить ячейку и вставить ниже"], "run all cells": ["запуск всех ячеек"], "run all cells above": ["запуск всех ячеек выше"], "run all cells below": ["запуск всех ячеек ниже"], "enter command mode": ["вход в командный режим"], "insert image": ["вставить изображение"], "cut cell attachments": ["вырезать вложения ячейки"], "copy cell attachments": ["копирование вложений ячейки"], "paste cell attachments": ["вставить вложения ячейки"], "split cell at cursor": ["разбить ячейку при наведении курсора"], "enter edit mode": ["вход в режим редактирования"], "select previous cell": ["выбрать предыдущую ячейку"], "select cell above": ["выбрать ячейку выше"], "select next cell": ["выбрать следующую ячейку"], "select cell below": ["выбрать ячейку ниже"], "extend selection above": ["расширить выбор выше"], "extend selected cells above": ["расширить выделенные ячейки выше"], "extend selection below": ["расширить выбор ниже"], "extend selected cells below": ["расширить выделенные ячейки ниже"], "cut selected cells": ["вырезать выбранные ячейки"], "copy selected cells": ["копировать выбранные ячейки"], "paste cells above": ["вставить ячейки выше"], "paste cells below": ["вставить ячейки ниже"], "insert cell above": ["вставить ячейку выше"], "insert cell below": ["вставить ячейку ниже"], "change cell to code": ["изменить ячейку на код"], "change cell to markdown": ["изменить ячейку на markdown"], "change cell to raw": ["изменить ячейку на raw"], "change cell to heading 1": ["изменить ячейку на заголовок 1"], "change cell to heading 2": ["изменить ячейку на заголовок 2"], "change cell to heading 3": ["изменить ячейку на заголовок 3"], "change cell to heading 4": ["изменить ячейку на заголовок 4"], "change cell to heading 5": ["изменить ячейку на заголовок 5"], "change cell to heading 6": ["изменить ячейку на заголовок 6"], "toggle cell output": ["переключение вывода ячейки"], "toggle output of selected cells": ["переключить вывод выбранных ячеек"], "toggle cell scrolling": ["переключить прокрутку ячейки"], "toggle output scrolling of selected cells": ["переключить прокрутку вывода выбранных ячеек"], "clear cell output": ["очистить вывод ячейки"], "clear output of selected cells": ["очистить вывод выбранных ячеек"], "move cells down": ["переместить ячейки вниз"], "move selected cells down": ["переместить выбранные ячейки вниз"], "move cells up": ["переместить ячейки вверх"], "move selected cells up": ["переместить выбранные ячейки вверх"], "toggle line numbers": ["переключить номера строк"], "show keyboard shortcuts": ["показать сочетания клавиш клавиатуры"], "delete cells": ["удалить ячейки"], "delete selected cells": ["удалить выбранные ячейки"], "undo cell deletion": ["отменить удаление ячейки"], "merge cell with previous cell": ["объединить ячейку с предыдущей ячейкой"], "merge cell above": ["объединить ячейку выше"], "merge cell with next cell": ["объединить ячейку со следующей ячейкой"], "merge cell below": ["объединить ячейку ниже"], "merge selected cells": ["объединить выбранные ячейки"], "merge cells": ["объединить ячейки"], "merge selected cells, or current cell with cell below if only one cell is selected": ["объединить выбранные ячейки или текущую с ячейкой ниже, если выбрана только одна ячейка"], "show command pallette": ["показать палитру команд"], "open the command palette": ["открыть палитру команд"], "toggle all line numbers": ["переключение всех номеров строк"], "toggles line numbers in all cells, and persist the setting": ["переключает номера строк во всех ячейках и сохраняет настройку"], "show all line numbers": ["показать все номера строк"], "show line numbers in all cells, and persist the setting": ["показать номера строк во всех ячейках и сохранить настройку"], "hide all line numbers": ["скрыть все номера строк"], "hide line numbers in all cells, and persist the setting": ["скрыть номера строк во всех ячейках и сохранить настройки"], "toggle header": ["переключение заголовка"], "switch between showing and hiding the header": ["переключение между отображением и скрытием заголовка"], "show the header": ["показать заголовок"], "hide the header": ["скрыть заголовок"], "toggle toolbar": ["переключить панель инструментов"], "switch between showing and hiding the toolbar": ["переключение между отображением и скрытием панели инструментов"], "show the toolbar": ["показать панель инструментов"], "hide the toolbar": ["скрыть всплывающую подсказку"], "close the pager": ["закрыть пейджер"], "ignore": ["игнорировать"], "move cursor up": ["переместить курсор вверх"], "move cursor down": ["переместить курсор вниз"], "scroll notebook down": ["прокрутить блокнот вниз"], "scroll notebook up": ["прокрутить блокнот вверх"], "scroll cell center": ["прокрутить ячейку в центр"], "Scroll the current cell to the center": ["Прокрутить текущую ячейку к центру"], "scroll cell top": ["прокрутить ячейку вверх"], "Scroll the current cell to the top": ["Прокрутить текущую ячейку к верху"], "duplicate notebook": ["дублировать блокнот"], "Create and open a copy of the current notebook": ["Создать и открыть копию текущего блокнота"], "trust notebook": ["доверять блокноту"], "Trust the current notebook": ["Доверять текущему блокноту"], "rename notebook": ["переименовать блокнот"], "Rename the current notebook": ["Переименовать текущий блокнот"], "toggle all cells output collapsed": ["переключение сворачивания вывода всех ячеек"], "Toggle the hidden state of all output areas": ["Переключить скрытое состояние всех областей вывода"], "toggle all cells output scrolled": ["переключение прокрутки вывода всех ячеек"], "Toggle the scrolling state of all output areas": ["Переключить состояние прокрутки всех областей вывода"], "clear all cells output": ["очистить вывод всех ячеек"], "Clear the content of all the outputs": ["Очистить содержимое всех выводов"], "save notebook": ["сохранить блокнот"], "Save and Checkpoint": ["Сохранение и контрольная точка"], "Warning: accessing Cell.cm_config directly is deprecated.": ["Предупреждение: прямой доступ к Cell.cm_config устарел."], "Unrecognized cell type: %s": ["Нераспознанный тип ячейки: %s"], "Unrecognized cell type": ["Нераспознанный тип ячейки"], "Error in cell toolbar callback %s": ["Ошибка в обратном вызове панели инструментов ячейки %s"], "Clipboard types: %s": ["Типы буфера обмена: %s"], "Dialog for paste from system clipboard": ["Диалоговое окно для вставки из системного буфера обмена"], "Ctrl-V": ["Ctrl-V"], "Cmd-V": ["Cmd-V"], "Press %s again to paste": ["Нажмите %s еще раз, чтобы вставить"], "Why is this needed? ": ["Зачем это нужно? "], "We can't get paste events in this browser without a text box. ": ["Мы не можем получить прошлые события в этом браузере без текстового поля. "], "There's an invisible text box focused in this dialog.": ["В этом диалоговом окне есть невидимое текстовое поле."], "%s to paste": ["%s для вставки"], "Can't execute cell since kernel is not set.": ["Невозможно выполнить ячейку, так как ядро не установлено."], "In": ["Ввод"], "Could not find a kernel matching %s. Please select a kernel:": ["Не удалось найти ядро, соответствующее %s. Пожалуйста, выберите ядро:"], "Continue Without Kernel": ["Продолжить без ядра"], "Set Kernel": ["Установить ядро"], "Kernel not found": ["Ядро не найдено"], "Creating Notebook Failed": ["Ошибка создания блокнота"], "The error was: %s": ["Ошибка в: %s"], "Run": ["Запуск"], "Code": ["<PERSON>од"], "Markdown": ["<PERSON><PERSON>"], "Raw NBConvert": ["Необработанный NBConvert"], "Heading": ["Заголовок"], "unrecognized cell type:": ["нераспознанный тип ячейки:"], "Failed to retrieve MathJax from '%s'": ["Не удалось получить MathJax из '%s'"], "Math/LaTeX rendering will be disabled.": ["Рендеринг Math/LaTeX будет отключен."], "Trusted Notebook": ["Доверенный блокнот"], "Trust Notebook": ["Доверять блокноту"], "None": ["Отсутствует"], "No checkpoints": ["Контрольные точки отсутствуют"], "Opens in a new window": ["Откроется в новом окне"], "Autosave in progress, latest changes may be lost.": ["Автосохранение продолжается, последние изменения могут быть потеряны."], "Unsaved changes will be lost.": ["Несохраненные изменения будут потеряны."], "The Kernel is busy, outputs may be lost.": ["Ядро занято, вывод может быть потерян."], "This notebook is version %1$s, but we only fully support up to %2$s.": ["Этот блокнот имеет версию %1$s, но мы полностью поддерживаем только до %2$s."], "You can still work with this notebook, but cell and output types introduced in later notebook versions will not be available.": ["Вы все еще можете работать с этим блокнотом, но типы ячеек и выходных данных, представленные в более поздних версиях блокнота, будут недоступны."], "Restart and Run All Cells": ["Перезапуск и запуск всех ячеек"], "Restart and Clear All Outputs": ["Перезапуск и очистка всего вывода"], "Restart": ["Перезапуск"], "Continue Running": ["Продолжить запуск"], "Reload": ["Перезагрузка"], "Overwrite": ["Переза<PERSON>ись"], "Trust": ["Доверять"], "Revert": ["Откат"], "Newer Notebook": ["Новый Notebook"], "Use markdown headings": ["Использовать заголовки markdown"], "Jupyter no longer uses special heading cells. Instead, write your headings in Markdown cells using # characters:": ["Jupyter больше не использует специальные ячейки заголовка. Вместо этого указывайте заголовки в ячейках Markdown, используя символы #:"], "## This is a level 2 heading": ["## Это заголовок 2 уровня"], "Restart kernel and re-run the whole notebook?": ["Перезапустить ядро и перезапустить все блокноты?"], "Are you sure you want to restart the current kernel and re-execute the whole notebook?  All variables and outputs will be lost.": ["Вы уверены, что хотите перезапустить текущее ядро и повторно выполнить весь блокнот?  Все переменные и выходные данные будут потеряны."], "Restart kernel and clear all output?": ["Перезапустить ядро и очистить весь вывод?"], "Do you want to restart the current kernel and clear all output?  All variables and outputs will be lost.": ["Вы хотите перезапустить текущее ядро и очистить весь вывод? Все переменные и выводы будут потеряны."], "Restart kernel?": ["Перезапустить ядро?"], "Do you want to restart the current kernel?  All variables will be lost.": ["Вы хотите перезапустить текущее ядро? Все переменные будут потеряны."], "Shutdown kernel?": ["Выключить ядро?"], "Do you want to shutdown the current kernel?  All variables will be lost.": ["Вы хотите выключить текущее ядро? Все переменные будут потеряны."], "Notebook changed": ["Блокнот изменен"], "The notebook file has changed on disk since the last time we opened or saved it. Do you want to overwrite the file on disk with the version open here, or load the version on disk (reload the page) ?": ["Файл блокнота изменился на диске с момента его последнего открытия или сохранения. Вы хотите перезаписать файл на диске открытой здесь версией или загрузить версию с диска (перезагрузить страницу)?"], "Notebook validation failed": ["Ошибка проверки блокнота"], "The save operation succeeded, but the notebook does not appear to be valid. The validation error was:": ["Операция сохранения прошла успешно, но блокнот, похоже, недействителен. Ошибка проверки:"], "A trusted Jupyter notebook may execute hidden malicious code when you open it. Selecting trust will immediately reload this notebook in a trusted state. For more information, see the Jupyter security documentation: ": ["Доверенный блокнот Jupyter может выполнять скрытый вредоносный код при открытии. При выборе параметра доверие этот блокнот будет немедленно перезагружен в доверенное состояние. Дополнительные сведения см. в документации по безопасности Jupiter: "], "here": ["здесь"], "Trust this notebook?": ["Доверять этому блокноту?"], "Notebook failed to load": ["Ошибка загрузки блокнота"], "The error was: ": ["Ошибка в: "], "See the error console for details.": ["Дополнительные сведения см. в консоли ошибок."], "The notebook also failed validation:": ["Блокнот также не прошла проверку:"], "An invalid notebook may not function properly. The validation error was:": ["Неисправный блокнот может работать неправильно. Ошибка проверки:"], "This notebook has been converted from an older notebook format to the current notebook format v(%s).": ["Этот блокнот был преобразован из более старого формата блокнота в текущий формат v(%s)."], "This notebook has been converted from a newer notebook format to the current notebook format v(%s).": ["Этот блокнот был преобразован из более нового формата блокнота в текущий формат v(%s)."], "The next time you save this notebook, the current notebook format will be used.": ["При следующем сохранении этого блокнота будет использоваться текущий формат блокнота."], "Older versions of Jupyter may not be able to read the new format.": ["Старые версии Jupiter могут быть не в состоянии прочитать новый формат."], "Some features of the original notebook may not be available.": ["Некоторые функции оригинального блокнота могут быть недоступны."], "To preserve the original version, close the notebook without saving it.": ["Чтобы сохранить исходную версию, закройте блокнот не сохраняя его."], "Notebook converted": ["Блокнот преобразован"], "(No name)": ["(Без имени)"], "An unknown error occurred while loading this notebook. This version can load notebook formats %s or earlier. See the server log for details.": ["При загрузке этого блокнота произошла неизвестная ошибка. Эта версия может загружать форматы блокнотов %s или более ранние. Дополнительные сведения см. в журнале сервера."], "Error loading notebook": ["Ошибка загрузки блокнота"], "Are you sure you want to revert the notebook to the latest checkpoint?": ["Вы уверены, что хотите вернуть блокнот к последней контрольной точке?"], "This cannot be undone.": ["Этого уже не исправить."], "The checkpoint was last updated at:": ["Последний раз контрольная точка обновлялась:"], "Revert notebook to checkpoint": ["Откатить блокнот до контрольной точки"], "Edit Mode": ["Режим редактирования"], "Command Mode": ["Командный режим"], "Kernel Created": ["Ядро создано"], "Connecting to kernel": ["Подключение к ядру"], "Not Connected": ["Не подключено"], "click to reconnect": ["нажмите для переподключения"], "Restarting kernel": ["Перезапуск ядра"], "Kernel Restarting": ["Перезапуск ядра"], "The kernel appears to have died. It will restart automatically.": ["Ядро, по-видимому, умерло. Оно будет перезапущено автоматически."], "Dead kernel": ["Убить ядро"], "Kernel Dead": ["Ядро мертво"], "Interrupting kernel": ["Прерывание ядра"], "No Connection to Kernel": ["Нет связи с ядром"], "A connection to the notebook server could not be established. The notebook will continue trying to reconnect. Check your network connection or notebook server configuration.": ["Не удалось установить соединение с сервером блокнота. Блокнот будет продолжать попытки повторного подключения. Проверьте сетевое подключение или конфигурацию сервера блокнота."], "Connection failed": ["Ошибка подключения"], "No kernel": ["Ядро отсутствует"], "Kernel is not running": ["Ядро не запущено"], "Don't Restart": ["Не перезагружать"], "Try Restarting Now": ["Попробуйте перезагрузить сейчас"], "The kernel has died, and the automatic restart has failed. It is possible the kernel cannot be restarted. If you are not able to restart the kernel, you will still be able to save the notebook, but running code will no longer work until the notebook is reopened.": ["Ядро умерло и автоматический перезапуск не удался. Вполне возможно, что ядро не может быть перезапущено. Если вам не удастся перезапустить ядро - вы все равно сможете сохранить блокнот, но запущенный код больше не будет работать до тех пор, пока блокнот не будет снова открыт."], "No Kernel": ["Ядро отсутствует"], "Failed to start the kernel": ["Не удалось запустить ядро"], "Kernel Busy": ["Ядро занято"], "Kernel starting, please wait...": ["Перезапуск ядра, пожалуйста подождите..."], "Kernel Idle": ["Ядро бездействует"], "Kernel ready": ["Ядро готово"], "Using kernel: ": ["Использование ядра: "], "Only candidate for language: %1$s was %2$s.": ["Единственный кандидат на язык: %1$s был %2$s."], "Loading notebook": ["Загрузка блокнота"], "Notebook loaded": ["Блокнот загружен"], "Saving notebook": ["Сохранение блокнота"], "Notebook saved": ["Блокнот сохранен"], "Notebook save failed": ["Ошибка сохранения блокнота"], "Notebook copy failed": ["Ошибка копирования блокнота"], "Checkpoint created": ["Контрольная точка создана"], "Checkpoint failed": ["Ошибка контрольной точки"], "Checkpoint deleted": ["Контрольная точка удалена"], "Checkpoint delete failed": ["Ошибка удаления контрольной точки"], "Restoring to checkpoint...": ["Восстановление до контрольной точки..."], "Checkpoint restore failed": ["Не удалось восстановить контрольную точку"], "Autosave disabled": ["Автосохранение отключено"], "Saving every %d sec.": ["Сохранение каждые %d секунд."], "Trusted": ["Доверенный"], "Not Trusted": ["Не доверять"], "click to expand output": ["щелкните чтобы развернуть вывод"], "click to expand output; double click to hide output": ["щелкните чтобы развернуть вывод; двойной щелчок чтобы скрыть вывод"], "click to unscroll output; double click to hide": ["щелчок для прокрутки вывода; двойной щелчок - чтобы скрыть"], "click to scroll output; double click to hide": ["щелчок для прокрутки вывода; двойной щелчок - чтобы скрыть"], "Javascript error adding output!": ["Ошибка Javascript при добавлении вывода!"], "See your browser Javascript console for more details.": ["Дополнительные сведения см. в разделе консоль Javascript вашего браузера."], "Out[%d]:": ["Вывод[%d]:"], "Unrecognized output: %s": ["Нераспознанный вывод: %s"], "Open the pager in an external window": ["Открыть пейджер во внешнем окне"], "Close the pager": ["Закрыть пейджер"], "Jupyter Pager": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "go to cell start": ["перейти к началу ячейки"], "go to cell end": ["перейти к концу ячейки"], "go one word left": ["перейти на слово влево"], "go one word right": ["перейти на слово вправо"], "delete word before": ["удалить слово перед"], "delete word after": ["удалить слово после"], "code completion or indent": ["завершение кода или отступ"], "tooltip": ["всплывающая подсказка"], "indent": ["отступ"], "dedent": ["dedent"], "select all": ["выбрать всё"], "undo": ["отменить"], "redo": ["повторить"], "Shift": ["Shift"], "Alt": ["Alt"], "Up": ["Ввер<PERSON>"], "Down": ["<PERSON><PERSON><PERSON><PERSON>"], "Left": ["Лево"], "Right": ["Право"], "Tab": ["Tab"], "Caps Lock": ["CapsLock"], "Esc": ["Esc"], "Ctrl": ["Ctrl"], "Enter": ["Enter"], "Page Up": ["Страница вверх"], "Page Down": ["Страница вниз"], "Home": ["До<PERSON><PERSON><PERSON>"], "End": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Space": ["Пробел"], "Backspace": ["Бэкспейс"], "Minus": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "PageUp": ["СтраницаВверх"], "The Jupyter Notebook has two different keyboard input modes.": ["Jupyter Notebook имеет два различных режима ввода с клавиатуры."], "<b>Edit mode</b> allows you to type code or text into a cell and is indicated by a green cell border.": ["<b>Режим редактирования</b> позволяет вводить код или текст в ячейку и обозначается зеленой рамкой ячейки."], "<b>Command mode</b> binds the keyboard to notebook level commands and is indicated by a grey cell border with a blue left margin.": ["<b>Командный режим</b> связывает клавиатуру с командами уровня блокнота и обозначается серой рамкой ячейки с синим левым краем."], "Close": ["Закрыть"], "Keyboard shortcuts": ["Сочетания клавиш"], "Command": ["Команда"], "Control": ["Управление"], "Option": ["Параметр"], "Return": ["Возврат"], "Command Mode (press %s to enable)": ["Командный режим (нажмите %s для включения)"], "Edit Shortcuts": ["Редактировать ярлыки"], "edit command-mode keyboard shortcuts": ["редактировать сочетания клавиш командного-режима"], "Edit Mode (press %s to enable)": ["Режим редактирования (нажмите %s для включения)"], "Autosave Failed!": ["Автосохранение не удалось!"], "Rename": ["Переименование"], "Enter a new notebook name:": ["Введите новое имя блокнота:"], "Rename Notebook": ["Переименовать блокнот"], "Invalid notebook name. Notebook names must have 1 or more characters and can contain any characters except :/\\. Please enter a new notebook name:": ["Недействительное имя блокнота. Имена блокнотов должны иметь 1 или более символов и могут содержать любые символы, кроме :/\\. Пожалуйста, введите новое имя блокнота:"], "Renaming...": ["Переименование..."], "Unknown error": ["Неизвестная ошибка"], "no checkpoint": ["контрольная точка отсутствует"], "Last Checkpoint: %s": ["Последняя контрольная точка: %s"], "(unsaved changes)": ["(несохраненные изменения)"], "(autosaved)": ["(автосохранение)"], "Warning: too many matches (%d). Some changes might not be shown or applied.": ["Предупреждение: слишком много совпадений (%d). Некоторые изменения могут быть не показаны или не применены."], "%d match": ["%d совпадение", "%d совпадения", "%d совпадений"], "More than 100 matches, aborting": ["Более 100 совпадений, прерывание"], "Use regex (JavaScript regex syntax)": ["Используйте регулярное выражение (синтаксис регулярных выражений JavaScript)"], "Replace in selected cells": ["Заменить в выбранных ячейках"], "Match case": ["Учитывать регистр"], "Find": ["Поиск"], "Replace": ["Замена"], "No matches, invalid or empty regular expression": ["Нет совпадений, недопустимое или пустое регулярное выражение"], "Replace All": ["Заменить всё"], "Find and Replace": ["Поиск и замена"], "find and replace": ["поиск и замена"], "Write raw LaTeX or other formats here, for use with nbconvert. It will not be rendered in the notebook. When passing through nbconvert, a Raw Cell's content is added to the output unmodified.": ["Пишите сюда необработанный LaTeX или другие форматы для использования с nbconvert. Он не будет отображаться в блокноте. При прохождении через nbconvert содержимое необработанных ячеек добавляется к выводу неизмененным."], "Grow the tooltip vertically (press shift-tab twice)": ["Растяните всплывающую подсказку вертикально (нажмите shift-tab дважды)"], "show the current docstring in pager (press shift-tab 4 times)": ["показать текущую строку документа в пейджере (нажмите shift-tab 4 раза)"], "Open in Pager": ["Открыть в пейджере"], "Tooltip will linger for 10 seconds while you type": ["Всплывающая подсказка будет задерживаться в течение 10 секунд, пока вы набираете текст"], "Welcome to the Notebook Tour": ["Добро пожаловать в тур по Notebook"], "You can use the left and right arrow keys to go backwards and forwards.": ["Вы можете использовать клавиши со стрелками влево и вправо чтобы двигаться назад и вперед."], "Filename": ["Имя файла"], "Click here to change the filename for this notebook.": ["Нажмите здесь чтобы изменить имя файла для этого блокнота."], "Notebook Menubar": ["Строка меню блокнота"], "The menubar has menus for actions on the notebook, its cells, and the kernel it communicates with.": ["Панель меню имеет меню для действий с блокнотом, ее ячейками и ядром, с которым она взаимодействует."], "Notebook Toolbar": ["Панель инструментов блокнота"], "The toolbar has buttons for the most common actions. Hover your mouse over each button for more information.": ["На панели инструментов есть кнопки для наиболее распространенных действий. Наведите курсор мыши на каждую кнопку для получения дополнительной информации."], "Mode Indicator": ["Индикатор режима"], "The Notebook has two modes: Edit Mode and Command Mode. In this area, an indicator can appear to tell you which mode you are in.": ["Notebook имеет два режима работы: режим редактирования и командный режим. В этой области может появиться индикатор, сообщающий вам, в каком режиме вы находитесь."], "Right now you are in Command Mode, and many keyboard shortcuts are available. In this mode, no icon is displayed in the indicator area.": ["Прямо сейчас вы находитесь в командном режиме и многие сочетания клавиш доступны. В этом режиме значок в области индикатора не отображается."], "Pressing <code>Enter</code> or clicking in the input text area of the cell switches to Edit Mode.": ["Нажатие кнопки <code>Enter</code> или щелчок в области ввода текста ячейки переключает ее в режим редактирования."], "Notice that the border around the currently active cell changed color. Typing will insert text into the currently active cell.": ["Обратите внимание, что граница вокруг текущей активной ячейки изменила цвет. Ввод приведет к вставке текста в текущую активную ячейку."], "Back to Command Mode": ["Возврат к командному режиму"], "Pressing <code>Esc</code> or clicking outside of the input text area takes you back to Command Mode.": ["Нажатие кнопки <code>Esc</code> или щелчок за пределами области ввода текста возвращает вас в командный режим."], "Keyboard Shortcuts": ["Сочетания клавиш"], "You can click here to get a list of all of the keyboard shortcuts.": ["Вы можете нажать здесь, чтобы получить список всех сочетаний клавиш."], "Kernel Indicator": ["Индикатор ядра"], "This is the Kernel indicator. It looks like this when the Kernel is idle.": ["Это и есть индикатор ядра. Он выглядит так, когда ядро простаивает."], "The Kernel indicator looks like this when the Kernel is busy.": ["Индикатор ядра выглядит так, когда ядро занято."], "Interrupting the Kernel": ["Прерывание ядра"], "To cancel a computation in progress, you can click here.": ["Чтобы отменить выполняемые вычисления - вы можете нажать здесь."], "Notification Area": ["Область уведомлений"], "Messages in response to user actions (Save, Interrupt, etc.) appear here.": ["Сообщения в ответ на действия пользователя (сохранение, прерывание и т. д.) появятся здесь."], "End of Tour": ["Конец обзора"], "This concludes the Jupyter Notebook User Interface Tour.": ["На этом завершается экскурсия по пользовательскому интерфейсу Jupyter Notebook."], "Edit Attachments": ["Редактировать вложения"], "Cell": ["Ячейка"], "Edit Metadata": ["Редактировать метаданные"], "Custom": ["Пользовательский"], "Set the MIME type of the raw cell:": ["Установите тип MIME необработанной ячейки:"], "Raw Cell MIME Type": ["Необработанный MIME-тип ячейки"], "Raw NBConvert Format": ["Необработанный формат NBConvert"], "Raw Cell Format": ["Необработанный формат ячейки"], "Slide": ["Слайд"], "Sub-Slide": ["Под-слайд"], "Fragment": ["Фрагмент"], "Skip": ["Пропустить"], "Notes": ["Примечания"], "Slide Type": ["Тип слайда"], "Slideshow": ["Слайд-шоу"], "Add tag": ["Добавить тег"], "Edit the list of tags below. All whitespace is treated as tag separators.": ["Отредактируйте список тегов ниже. Все пробелы рассматриваются как разделители тегов."], "Edit the tags": ["Редактировать теги"], "Edit Tags": ["Редактировать теги"], "Shutdown": ["Выключение"], "Create a new notebook with %s": ["Создать новый блокнот с %s"], "An error occurred while creating a new notebook.": ["При создании нового блокнота произошла ошибка."], "Creating File Failed": ["Ошибка создания файла"], "An error occurred while creating a new file.": ["При создании нового файла произошла ошибка."], "Creating Folder Failed": ["Ошибка создания папки"], "An error occurred while creating a new folder.": ["При создании новой папки произошла ошибка."], "Failed to read file": ["Ошибка чтения файла"], "Failed to read file %s": ["Ошибка чтения файла %s"], "The file size is %d MB. Do you still want to upload it?": ["Размер файла - %d MB. Вы все еще хотите загрузить его?"], "Large file size warning": ["Предупреждение о большом размере файла"], "Server error: ": ["Ошибка сервера: "], "The notebook list is empty.": ["Список блокнотов пуст."], "Click here to rename, delete, etc.": ["Нажмите здесь, чтобы переименовать, удалить и т.д."], "Running": ["Запустить"], "Enter a new file name:": ["Введите новое имя файла:"], "Enter a new directory name:": ["Введите новое название каталога:"], "Enter a new name:": ["Введите новое имя:"], "Rename file": ["Переименовать файл"], "Rename directory": ["Переименовать директорию"], "Rename notebook": ["Переименовать блокнот"], "Move": ["Перемещение"], "An error occurred while renaming \"%1$s\" to \"%2$s\".": ["Произошла ошибка при переименовании \"%1$s\" в \"%2$s\"."], "Rename Failed": ["Ошибка переименования"], "Enter a new destination directory path for this item:": ["Введите новый путь к каталогу назначения для этого %d элемента:", "Введите новый путь к каталогу назначения для этих %d элементов:", "Введите новый путь к каталогу назначения для этих %d элементов:"], "Move an Item": ["Переместить %d элемент", "Переместить %d элемента", "Переместить %d элементов"], "An error occurred while moving \"%1$s\" from \"%2$s\" to \"%3$s\".": ["Произошла ошибка при перемещении \"%1$s\" из \"%2$s\" в \"%3$s\"."], "Move Failed": ["Ошибка перемещения"], "Are you sure you want to permanently delete: \"%s\"?": ["Вы уверены, что хотите навсегда удалить файл или папку \"%s\"?", "Вы уверены, что хотите навсегда удалить %d выбранных файла или папки?", "Вы уверены, что хотите навсегда удалить %d выбранных файлов или папок?"], "An error occurred while deleting \"%s\".": ["Произошла ошибка при удалении \"%s\"."], "Delete Failed": ["Ошибка удаления"], "Are you sure you want to duplicate: \"%s\"?": ["Вы уверены, что хотите скопировать файл \"%s\"?", "Вы уверены, что хотите скопировать %d выбранных файла?", "Вы уверены, что хотите скопировать %d выбранных файлов?"], "Duplicate": ["Скопировать"], "An error occurred while duplicating \"%s\".": ["Произошла ошибка при копировании \"%s\"."], "Duplicate Failed": ["Ошибка дублирования"], "Upload": ["Загрузить"], "Invalid file name": ["Неверное имя файла"], "File names must be at least one character and not start with a period": ["Имена файлов должны состоять как минимум из одного символа и не начинаться с точки"], "Cannot upload invalid Notebook": ["Невозможно загрузить недействительный Notebook"], "There is already a file named \"%s\". Do you want to replace it?": ["Там уже имеется файл с именем \"%s\". Вы хотите заменить его?"], "Replace file": ["Заменить файл"]}}}