# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-07-07 12:48-0500\n"
"PO-Revision-Date: 2020-07-06 11:05+0500\n"
"Language-Team: TranslAster <https://github.com/translaster>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"
"X-Generator: Poedit 2.3.1\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Q <<EMAIL>>\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Language: ru_RU\n"

#: notebook/templates/404.html:3
msgid "You are requesting a page that does not exist!"
msgstr "Вы запрашиваете страницу, которая не существует!"

#: notebook/templates/edit.html:37
msgid "current mode"
msgstr "текущий режим"

#: notebook/templates/edit.html:48 notebook/templates/notebook.html:78
msgid "File"
msgstr "Файл"

#: notebook/templates/edit.html:50 notebook/templates/tree.html:57
msgid "New"
msgstr "Новый"

#: notebook/templates/edit.html:51
msgid "Save"
msgstr "Сохранить"

#: notebook/templates/edit.html:52 notebook/templates/tree.html:36
msgid "Rename"
msgstr "Переименовать"

#: notebook/templates/edit.html:53 notebook/templates/tree.html:38
msgid "Download"
msgstr "Загрузить"

#: notebook/templates/edit.html:56 notebook/templates/notebook.html:131
#: notebook/templates/tree.html:41
msgid "Edit"
msgstr "Редактировать"

#: notebook/templates/edit.html:58
msgid "Find"
msgstr "Поиск"

#: notebook/templates/edit.html:59
msgid "Find &amp; Replace"
msgstr "Поиск &amp; Замена"

#: notebook/templates/edit.html:61
msgid "Key Map"
msgstr "Сопоставления клавиш"

#: notebook/templates/edit.html:62
msgid "Default"
msgstr "По-умолчанию"

#: notebook/templates/edit.html:63
msgid "Sublime Text"
msgstr "Sublime Text"

#: notebook/templates/edit.html:68 notebook/templates/notebook.html:159
#: notebook/templates/tree.html:40
msgid "View"
msgstr "Вид"

#: notebook/templates/edit.html:70 notebook/templates/notebook.html:162
msgid "Show/Hide the logo and notebook title (above menu bar)"
msgstr "Показать/cкрыть логотип и название блокнота (над строкой меню)"

#: notebook/templates/edit.html:71 notebook/templates/notebook.html:163
msgid "Toggle Header"
msgstr "Показать/скрыть заголовок"

#: notebook/templates/edit.html:72 notebook/templates/notebook.html:171
msgid "Toggle Line Numbers"
msgstr "Показать/скрыть номера строк"

#: notebook/templates/edit.html:75
msgid "Language"
msgstr "Язык"

#: notebook/templates/error.html:23
msgid "The error was:"
msgstr "Ошибка в:"

#: notebook/templates/login.html:24
msgid "Password or token:"
msgstr "Пароль или токен:"

#: notebook/templates/login.html:26
msgid "Password:"
msgstr "Пароль:"

#: notebook/templates/login.html:31
msgid "Log in"
msgstr "Вход"

#: notebook/templates/login.html:39
msgid "No login available, you shouldn't be seeing this page."
msgstr "Без входа недоступно, вы не должны видеть эту страницу."

#: notebook/templates/logout.html:24
#, python-format
msgid "Proceed to the <a href=\"%(base_url)s\">dashboard"
msgstr "Перейдите к <a href=\"%(base_url)s\">панели мониторинга"

#: notebook/templates/logout.html:26
#, python-format
msgid "Proceed to the <a href=\"%(base_url)slogin\">login page"
msgstr "Перейдите к <a href=\"%(base_url)slogin\">странице входа"

#: notebook/templates/notebook.html:62
msgid "Menu"
msgstr "Меню"

#: notebook/templates/notebook.html:65 notebook/templates/notebook.html:254
msgid "Kernel"
msgstr "Ядро"

#: notebook/templates/notebook.html:68
msgid "This notebook is read-only"
msgstr "Этот блокнот только для чтения"

#: notebook/templates/notebook.html:81
msgid "New Notebook"
msgstr "Новый блокнот"

#: notebook/templates/notebook.html:85
msgid "Opens a new window with the Dashboard view"
msgstr "Открывает новое окно с видом панели мониторинга"

#: notebook/templates/notebook.html:86
msgid "Open..."
msgstr "Открыть..."

#: notebook/templates/notebook.html:90
msgid "Open a copy of this notebook's contents and start a new kernel"
msgstr "Откройте копию содержимого этого блокнота и запустите новое ядро"

#: notebook/templates/notebook.html:91
msgid "Make a Copy..."
msgstr "Сделать копию..."

#: notebook/templates/notebook.html:92
msgid "Rename..."
msgstr "Переименовать..."

#: notebook/templates/notebook.html:93
msgid "Save and Checkpoint"
msgstr "Сохранение и контрольная точка"

#: notebook/templates/notebook.html:96
msgid "Revert to Checkpoint"
msgstr "Вернуться к контрольной точке"

#: notebook/templates/notebook.html:106
msgid "Print Preview"
msgstr "Предпросмотр печати"

#: notebook/templates/notebook.html:107
msgid "Download as"
msgstr "Скачать как"

#: notebook/templates/notebook.html:109
msgid "Notebook (.ipynb)"
msgstr "Notebook (.ipynb)"

#: notebook/templates/notebook.html:110
msgid "Script"
msgstr "Скрипт"

#: notebook/templates/notebook.html:111
msgid "HTML (.html)"
msgstr "HTML (.html)"

#: notebook/templates/notebook.html:112
msgid "Markdown (.md)"
msgstr "Markdown (.md)"

#: notebook/templates/notebook.html:113
msgid "reST (.rst)"
msgstr "reST (.rst)"

#: notebook/templates/notebook.html:114
msgid "LaTeX (.tex)"
msgstr "LaTeX (.tex)"

#: notebook/templates/notebook.html:115
msgid "PDF via LaTeX (.pdf)"
msgstr "PDF через LaTeX (.pdf)"

#: notebook/templates/notebook.html:118
msgid "Deploy as"
msgstr "Использовать как"

#: notebook/templates/notebook.html:123
msgid "Trust the output of this notebook"
msgstr "Доверять выводу этого блокнота"

#: notebook/templates/notebook.html:124
msgid "Trust Notebook"
msgstr "Доверять блокноту"

#: notebook/templates/notebook.html:127
msgid "Shutdown this notebook's kernel, and close this window"
msgstr "Выключить ядро этого блокнота и закрыть это окно"

#: notebook/templates/notebook.html:128
msgid "Close and Halt"
msgstr "Закрыть и остановить"

#: notebook/templates/notebook.html:133
msgid "Cut Cells"
msgstr "Вырезать ячейки"

#: notebook/templates/notebook.html:134
msgid "Copy Cells"
msgstr "Копировать ячейки"

#: notebook/templates/notebook.html:135
msgid "Paste Cells Above"
msgstr "Вставить ячейки выше"

#: notebook/templates/notebook.html:136
msgid "Paste Cells Below"
msgstr "Вставить ячейки ниже"

#: notebook/templates/notebook.html:137
msgid "Paste Cells &amp; Replace"
msgstr "Вставить ячейки &amp; заменить"

#: notebook/templates/notebook.html:138
msgid "Delete Cells"
msgstr "Удалить ячейки"

#: notebook/templates/notebook.html:139
msgid "Undo Delete Cells"
msgstr "Отменить удаление ячеек"

#: notebook/templates/notebook.html:141
msgid "Split Cell"
msgstr "Разбить ячейку"

#: notebook/templates/notebook.html:142
msgid "Merge Cell Above"
msgstr "Объединить с ячейкой выше"

#: notebook/templates/notebook.html:143
msgid "Merge Cell Below"
msgstr "Объединить с ячейкой ниже"

#: notebook/templates/notebook.html:145
msgid "Move Cell Up"
msgstr "Переместить ячейку выше"

#: notebook/templates/notebook.html:146
msgid "Move Cell Down"
msgstr "Переместить ячейку ниже"

#: notebook/templates/notebook.html:148
msgid "Edit Notebook Metadata"
msgstr "Редактировать метаданные блокнота"

#: notebook/templates/notebook.html:150
msgid "Find and Replace"
msgstr "Поиск и замена"

#: notebook/templates/notebook.html:152
msgid "Cut Cell Attachments"
msgstr "Вырезать вложения ячейки"

#: notebook/templates/notebook.html:153
msgid "Copy Cell Attachments"
msgstr "Копировать вложения ячейки"

#: notebook/templates/notebook.html:154
msgid "Paste Cell Attachments"
msgstr "Вставить вложения ячейки"

#: notebook/templates/notebook.html:156
msgid "Insert Image"
msgstr "Вставить изображение"

#: notebook/templates/notebook.html:166
msgid "Show/Hide the action icons (below menu bar)"
msgstr "Показать/скрыть значки действий (ниже строки меню)"

#: notebook/templates/notebook.html:167
msgid "Toggle Toolbar"
msgstr "Показать/скрыть панель мониторинга"

#: notebook/templates/notebook.html:170
msgid "Show/Hide line numbers in cells"
msgstr "Показать/скрыть номера строк в ячейках"

#: notebook/templates/notebook.html:174
msgid "Cell Toolbar"
msgstr "Панель инструментов ячейки"

#: notebook/templates/notebook.html:179
msgid "Insert"
msgstr "Вставка"

#: notebook/templates/notebook.html:182
msgid "Insert an empty Code cell above the currently active cell"
msgstr "Вставьте пустую ячейку кода над текущей активной ячейкой"

#: notebook/templates/notebook.html:183
msgid "Insert Cell Above"
msgstr "Вставить ячейку выше"

#: notebook/templates/notebook.html:185
msgid "Insert an empty Code cell below the currently active cell"
msgstr "Вставьте пустую ячейку кода под текущей активной ячейкой"

#: notebook/templates/notebook.html:186
msgid "Insert Cell Below"
msgstr "Вставить ячейку ниже"

#: notebook/templates/notebook.html:189
msgid "Cell"
msgstr "Ячейка"

#: notebook/templates/notebook.html:191
msgid "Run this cell, and move cursor to the next one"
msgstr "Запустить эту ячейку и переместить курсор на следующую"

#: notebook/templates/notebook.html:192
msgid "Run Cells"
msgstr "Запустить ячейки"

#: notebook/templates/notebook.html:193
msgid "Run this cell, select below"
msgstr "Запустить эту ячейку, выбрать ниже"

#: notebook/templates/notebook.html:194
msgid "Run Cells and Select Below"
msgstr "Запустить ячейки и выбрать ниже"

#: notebook/templates/notebook.html:195
msgid "Run this cell, insert below"
msgstr "Запустить эту ячейку, вставить ниже"

#: notebook/templates/notebook.html:196
msgid "Run Cells and Insert Below"
msgstr "Запустить ячейки и вставить ниже"

#: notebook/templates/notebook.html:197
msgid "Run all cells in the notebook"
msgstr "Запустить все ячейки в этом блокноте"

#: notebook/templates/notebook.html:198
msgid "Run All"
msgstr "Запустить всё"

#: notebook/templates/notebook.html:199
msgid "Run all cells above (but not including) this cell"
msgstr "Запустите все ячейки выше (но не включая) этой ячейки"

#: notebook/templates/notebook.html:200
msgid "Run All Above"
msgstr "Запустить всё выше"

#: notebook/templates/notebook.html:201
msgid "Run this cell and all cells below it"
msgstr "Запустить эту ячейку и все ячейки под ней"

#: notebook/templates/notebook.html:202
msgid "Run All Below"
msgstr "Запустить всё ниже"

#: notebook/templates/notebook.html:205
msgid ""
"All cells in the notebook have a cell type. By default, new cells are "
"created as 'Code' cells"
msgstr ""
"Все ячейки в блокноте имеют определенный тип ячеек. По умолчанию новые "
"ячейки создаются как ячейки \"Код\""

#: notebook/templates/notebook.html:206
msgid "Cell Type"
msgstr "Тип ячейки"

#: notebook/templates/notebook.html:209
msgid ""
"Contents will be sent to the kernel for execution, and output will display "
"in the footer of cell"
msgstr ""
"Содержимое будет отправлено ядру для выполнения, а выходные данные будут "
"отображаться в нижнем колонтитуле ячейки"

#: notebook/templates/notebook.html:212
msgid "Contents will be rendered as HTML and serve as explanatory text"
msgstr ""
"Содержимое будет отображаться в формате HTML и служить пояснительным "
"текстом"

#: notebook/templates/notebook.html:213 notebook/templates/notebook.html:298
msgid "Markdown"
msgstr "Markdown"

#: notebook/templates/notebook.html:215
msgid "Contents will pass through nbconvert unmodified"
msgstr "Содержимое будет проходить через nbconvert неизмененным"

#: notebook/templates/notebook.html:216
msgid "Raw NBConvert"
msgstr "Необработанный NBConvert"

#: notebook/templates/notebook.html:220
msgid "Current Outputs"
msgstr "Текущий вывод"

#: notebook/templates/notebook.html:223
msgid "Hide/Show the output of the current cell"
msgstr "Показать/скрыть вывод текущей ячейки"

#: notebook/templates/notebook.html:224 notebook/templates/notebook.html:240
msgid "Toggle"
msgstr "Триггер"

#: notebook/templates/notebook.html:227
msgid "Scroll the output of the current cell"
msgstr "Прокрутить вывод текущей ячейки"

#: notebook/templates/notebook.html:228 notebook/templates/notebook.html:244
msgid "Toggle Scrolling"
msgstr "Триггер скроллинга"

#: notebook/templates/notebook.html:231
msgid "Clear the output of the current cell"
msgstr "Очистите выходные данные текущей ячейки"

#: notebook/templates/notebook.html:232 notebook/templates/notebook.html:248
msgid "Clear"
msgstr "Очистка"

#: notebook/templates/notebook.html:236
msgid "All Output"
msgstr "Весь вывод"

#: notebook/templates/notebook.html:239
msgid "Hide/Show the output of all cells"
msgstr "Скрыть/показать вывод всех ячеек"

#: notebook/templates/notebook.html:243
msgid "Scroll the output of all cells"
msgstr "Прокрутите вывод всех ячеек"

#: notebook/templates/notebook.html:247
msgid "Clear the output of all cells"
msgstr "Очистите выходные данные всех ячеек"

#: notebook/templates/notebook.html:257
msgid "Send Keyboard Interrupt (CTRL-C) to the Kernel"
msgstr "Отправить прерывание клавиатуры (CTRL-C) ядру"

#: notebook/templates/notebook.html:258
msgid "Interrupt"
msgstr "Прервать"

#: notebook/templates/notebook.html:261
msgid "Restart the Kernel"
msgstr "Перезапустить ядро"

#: notebook/templates/notebook.html:262
msgid "Restart"
msgstr "Перезапуск"

#: notebook/templates/notebook.html:265
msgid "Restart the Kernel and clear all output"
msgstr "Перезапустить ядро и очистить все выходные данные"

#: notebook/templates/notebook.html:266
msgid "Restart &amp; Clear Output"
msgstr "Перезапустить &amp; очистить вывод"

#: notebook/templates/notebook.html:269
msgid "Restart the Kernel and re-run the notebook"
msgstr "Перезапустить ядро и перезапустить блокнот"

#: notebook/templates/notebook.html:270
msgid "Restart &amp; Run All"
msgstr "Перезапустить &amp; Запустить всё"

#: notebook/templates/notebook.html:273
msgid "Reconnect to the Kernel"
msgstr "Переподключение к ядру"

#: notebook/templates/notebook.html:274
msgid "Reconnect"
msgstr "Переподключение"

#: notebook/templates/notebook.html:282
msgid "Change kernel"
msgstr "Изменение ядра"

#: notebook/templates/notebook.html:287
msgid "Help"
msgstr "Справка"

#: notebook/templates/notebook.html:290
msgid "A quick tour of the notebook user interface"
msgstr "Краткий обзор пользовательского интерфейса блокнота"

#: notebook/templates/notebook.html:290
msgid "User Interface Tour"
msgstr "Обзор пользовательского интерфейса"

#: notebook/templates/notebook.html:291
msgid "Opens a tooltip with all keyboard shortcuts"
msgstr "Открывает инструмент со всеми сочетаниями клавиш"

#: notebook/templates/notebook.html:291
msgid "Keyboard Shortcuts"
msgstr "Сочетания клавиш"

#: notebook/templates/notebook.html:292
msgid "Opens a dialog allowing you to edit Keyboard shortcuts"
msgstr "Открывает диалог, позволяющий редактировать сочетания клавиш"

#: notebook/templates/notebook.html:292
msgid "Edit Keyboard Shortcuts"
msgstr "Редактировать сочетания клавиш"

#: notebook/templates/notebook.html:297
msgid "Notebook Help"
msgstr "Справка по блокноту"

#: notebook/templates/notebook.html:303
msgid "Opens in a new window"
msgstr "Открыть в новом окне"

#: notebook/templates/notebook.html:319
msgid "About Jupyter Notebook"
msgstr "О блокноте Jupyter"

#: notebook/templates/notebook.html:319
msgid "About"
msgstr "О программе"

#: notebook/templates/page.html:114
msgid "Jupyter Notebook requires JavaScript."
msgstr "Блокнот Jupyter требует JavaScript."

#: notebook/templates/page.html:115
msgid "Please enable it to proceed. "
msgstr "Пожалуйста, позвольте продолжить работу. "

#: notebook/templates/page.html:121
msgid "dashboard"
msgstr "панель мониторинга"

#: notebook/templates/page.html:132
msgid "Logout"
msgstr "Разлогиниться"

#: notebook/templates/page.html:134
msgid "Login"
msgstr "Вход"

#: notebook/templates/tree.html:23 notebook/templates/tree.html:101
msgid "Files"
msgstr "Файлы"

#: notebook/templates/tree.html:24 notebook/templates/tree.html:100
msgid "Running"
msgstr "Запустить"

#: notebook/templates/tree.html:25
msgid "Clusters"
msgstr "Кластеры"

#: notebook/templates/tree.html:32
msgid "Select items to perform actions on them."
msgstr "Выберите элементы для выполнения действий над ними."

#: notebook/templates/tree.html:35
msgid "Duplicate selected"
msgstr "Копировать выбранное"

#: notebook/templates/tree.html:35
msgid "Duplicate"
msgstr "Скопировать"

#: notebook/templates/tree.html:36
msgid "Rename selected"
msgstr "Переименовать выделенное"

#: notebook/templates/tree.html:37
msgid "Move selected"
msgstr "Переместить выделенное"

#: notebook/templates/tree.html:37
msgid "Move"
msgstr "Переместить"

#: notebook/templates/tree.html:38
msgid "Download selected"
msgstr "Скачать выбранное"

#: notebook/templates/tree.html:39
msgid "Shutdown selected notebook(s)"
msgstr "Выключить выбранный(-е) блокнот(ы)"

#: notebook/templates/notebook.html:278 notebook/templates/tree.html:39
msgid "Shutdown"
msgstr "Выключение"

#: notebook/templates/tree.html:40
msgid "View selected"
msgstr "Просмотр выбранного"

#: notebook/templates/tree.html:41
msgid "Edit selected"
msgstr "Редактировать выделенное"

#: notebook/templates/tree.html:42
msgid "Delete selected"
msgstr "Удалить выделенное"

#: notebook/templates/tree.html:50
msgid "Click to browse for a file to upload."
msgstr "Нажмите, чтобы найти файл для загрузки."

#: notebook/templates/tree.html:51
msgid "Upload"
msgstr "Загрузить"

#: notebook/templates/tree.html:65
msgid "Text File"
msgstr "Текстовый файл"

#: notebook/templates/tree.html:68
msgid "Folder"
msgstr "Папка"

#: notebook/templates/tree.html:72
msgid "Terminal"
msgstr "Терминал"

#: notebook/templates/tree.html:76
msgid "Terminals Unavailable"
msgstr "Терминалы недоступны"

#: notebook/templates/tree.html:82
msgid "Refresh notebook list"
msgstr "Обновить список блокнотов"

#: notebook/templates/tree.html:90
msgid "Select All / None"
msgstr "Выбрать всё / ничего"

#: notebook/templates/tree.html:93
msgid "Select..."
msgstr "Выбрать..."

#: notebook/templates/tree.html:98
msgid "Select All Folders"
msgstr "Выбрать все папки"

#: notebook/templates/tree.html:98
msgid "Folders"
msgstr "Папки"

#: notebook/templates/tree.html:99
msgid "Select All Notebooks"
msgstr "Выбрать все блокноты"

#: notebook/templates/tree.html:99
msgid "All Notebooks"
msgstr "Все блокноты"

#: notebook/templates/tree.html:100
msgid "Select Running Notebooks"
msgstr "Запустить выбранные блокноты"

#: notebook/templates/tree.html:101
msgid "Select All Files"
msgstr "Выбрать все файлы"

#: notebook/templates/tree.html:114
msgid "Last Modified"
msgstr "Последнее изменение"

#: notebook/templates/tree.html:120
msgid "Name"
msgstr "Название"

#: notebook/templates/tree.html:130
msgid "Currently running Jupyter processes"
msgstr "Текущие запущенные процессы Jupyter"

#: notebook/templates/tree.html:134
msgid "Refresh running list"
msgstr "Обновить список запущенных"

#: notebook/templates/tree.html:150
msgid "There are no terminals running."
msgstr "Эти терминалы не запущены."

#: notebook/templates/tree.html:152
msgid "Terminals are unavailable."
msgstr "Терминалы недоступны."

#: notebook/templates/tree.html:162
msgid "Notebooks"
msgstr "Блокноты"

#: notebook/templates/tree.html:169
msgid "There are no notebooks running."
msgstr "Эти блокноты не запущены."

#: notebook/templates/tree.html:178
msgid "Clusters tab is now provided by IPython parallel."
msgstr "Вкладка кластеров обеспечивается оболочкой IPython параллельно."

#: notebook/templates/tree.html:179
msgid ""
"See '<a href=\"https://github.com/ipython/ipyparallel\">IPython parallel</"
"a>' for installation details."
msgstr ""
"Смотрите '<a href=\"https://github.com/ipython/ipyparallel\">IPython "
"parallel</a>' для подробной информации об установке."
