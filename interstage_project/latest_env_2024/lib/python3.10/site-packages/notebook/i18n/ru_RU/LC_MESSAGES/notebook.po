# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-07-08 21:52-0500\n"
"PO-Revision-Date: 2020-07-06 17:38+0500\n"
"Language-Team: TranslAster <https://github.com/translaster>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"
"X-Generator: Poedit 2.3.1\n"
"Last-Translator: Dmit<PERSON>y Q <<EMAIL>>\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Language: ru_RU\n"

#: notebook/notebookapp.py:53
msgid "The Jupyter Notebook requires tornado >= 4.0"
msgstr "Для Jupyter Notebook требуется tornado >= 4.0"

#: notebook/notebookapp.py:57
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have < 1.1.0"
msgstr "Для Jupyter Notebook требуется tornado >= 4.0, но у вас < 1.1.0"

#: notebook/notebookapp.py:59
#, python-format
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have %s"
msgstr "Для Jupyter Notebook требуется tornado >= 4.0, но у вас %s"

#: notebook/notebookapp.py:209
msgid "The `ignore_minified_js` flag is deprecated and no longer works."
msgstr "Флаг `ignore_minified_js` устарел и больше не работает."

#: notebook/notebookapp.py:210
#, python-format
msgid ""
"Alternatively use `%s` when working on the notebook's Javascript and LESS"
msgstr ""
"В качестве альтернативы используйте `%s` при работе с блокнотами Javascript "
"и LESS"

#: notebook/notebookapp.py:211
msgid ""
"The `ignore_minified_js` flag is deprecated and will be removed in Notebook "
"6.0"
msgstr "Флаг `ignore_minified_js` устарел и будет удален в Notebook 6.0"

#: notebook/notebookapp.py:389
msgid "List currently running notebook servers."
msgstr "Список запущенных в данный момент серверов блокнотов."

#: notebook/notebookapp.py:393
msgid "Produce machine-readable JSON output."
msgstr "Произведите машиночитаемый вывод JSON."

#: notebook/notebookapp.py:397
msgid ""
"If True, each line of output will be a JSON object with the details from the "
"server info file."
msgstr ""
"Если True, то каждая строка вывода будет представлять собой объект JSON с "
"подробностями из файла информации сервера."

#: notebook/notebookapp.py:402
msgid "Currently running servers:"
msgstr "В настоящее время работают сервера:"

#: notebook/notebookapp.py:419
msgid "Don't open the notebook in a browser after startup."
msgstr "Не открывайте блокнот в браузере после запуска."

#: notebook/notebookapp.py:423
msgid ""
"DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib."
msgstr ""
"ОТКЛЮЧЕНО: используйте %pylab или %matplotlib в блокноте чтобы включить "
"matplotlib."

#: notebook/notebookapp.py:439
msgid "Allow the notebook to be run from root user."
msgstr "Разрешить запускать блокнот от пользователя root."

#: notebook/notebookapp.py:470
msgid ""
"The Jupyter HTML Notebook.\n"
"    \n"
"    This launches a Tornado based HTML Notebook Server that serves up an "
"HTML5/Javascript Notebook client."
msgstr ""
"The Jupyter HTML Notebook.\n"
"    \n"
"    Запускает сервер HTML Notebook на базе Tornado, обслуживающий клиент "
"HTML5/Javascript Notebook."

#: notebook/notebookapp.py:509
msgid ""
"Deprecated: Use minified JS file or not, mainly use during dev to avoid JS "
"recompilation"
msgstr ""
"Устарело: используйте уменьшенный файл JS или нет, в основном используется "
"во время разработки чтобы избежать перекомпиляции JS"

#: notebook/notebookapp.py:540
msgid "Set the Access-Control-Allow-Credentials: true header"
msgstr "Установка контроль-доступа-разрешенные-учетные_данные: заголовок true"

#: notebook/notebookapp.py:544
msgid "Whether to allow the user to run the notebook as root."
msgstr "Следует ли разрешить пользователю запускать блокнот от имени root."

#: notebook/notebookapp.py:548
msgid "The default URL to redirect to from `/`"
msgstr "URL-адрес по умолчанию для перенаправления на `/`"

#: notebook/notebookapp.py:552
msgid "The IP address the notebook server will listen on."
msgstr "IP-адрес, который будет прослушиваться сервером блокнота."

#: notebook/notebookapp.py:565
#, python-format
msgid ""
"Cannot bind to localhost, using 127.0.0.1 as default ip\n"
"%s"
msgstr ""
"Невозможно привязаться к localhost, используйте 127.0.0.1 в качестве ip-"
"адреса по умолчанию\n"
"%s"

#: notebook/notebookapp.py:579
msgid "The port the notebook server will listen on."
msgstr "Порт сервера блокнотов для прослушивания."

#: notebook/notebookapp.py:583
msgid ""
"The number of additional ports to try if the specified port is not available."
msgstr ""
"Количество дополнительных портов, которые нужно попробовать, если указанный "
"порт недоступен."

#: notebook/notebookapp.py:587
msgid "The full path to an SSL/TLS certificate file."
msgstr "Полный путь к файлу сертификата с SSL/TLS."

#: notebook/notebookapp.py:591
msgid "The full path to a private key file for usage with SSL/TLS."
msgstr "Полный путь к файлу приватного ключа для использования с SSL/TLS."

#: notebook/notebookapp.py:595
msgid ""
"The full path to a certificate authority certificate for SSL/TLS client "
"authentication."
msgstr ""
"Полный путь к сертификату центра сертификации для SSL/TLS аутентификации "
"клиента."

#: notebook/notebookapp.py:599
msgid "The file where the cookie secret is stored."
msgstr "Файл, в котором хранится пароль cookie."

#: notebook/notebookapp.py:628
#, python-format
msgid "Writing notebook server cookie secret to %s"
msgstr "Запись пароля cookie сервера блокнота в %s"

#: notebook/notebookapp.py:635
#, python-format
msgid "Could not set permissions on %s"
msgstr "Не удалось установить разрешения для %s"

#: notebook/notebookapp.py:640
msgid ""
"Token used for authenticating first-time connections to the server.\n"
"\n"
"        When no password is enabled,\n"
"        the default is to generate a new, random token.\n"
"\n"
"        Setting to an empty string disables authentication altogether, which "
"is NOT RECOMMENDED.\n"
"        "
msgstr ""
"Токен, используемый для проверки подлинности при первом подключении к "
"серверу.\n"
"\n"
"        Когда пароль не включен,\n"
"        по умолчанию генерируется новый случайный токен.\n"
"\n"
"        Установка пустой строки полностью отключает аутентификацию, что НЕ "
"РЕКОМЕНДУЕТСЯ.\n"
"        "

#: notebook/notebookapp.py:650
msgid ""
"One-time token used for opening a browser.\n"
"        Once used, this token cannot be used again.\n"
"        "
msgstr ""
"Одноразовый токен, используемый для открытия в браузере. \n"
"        После использования этот токен не может быть использован снова.\n"
"        "

#: notebook/notebookapp.py:726
msgid ""
"Specify Where to open the notebook on startup. This is the\n"
"        `new` argument passed to the standard library method `webbrowser."
"open`.\n"
"        The behaviour is not guaranteed, but depends on browser support. "
"Valid\n"
"        values are:\n"
"            2 opens a new tab,\n"
"            1 opens a new window,\n"
"            0 opens in an existing window.\n"
"        See the `webbrowser.open` documentation for details.\n"
"        "
msgstr ""
"Укажите где открывать блокнот при запуске. Это \"новый\"\n"
"        аргумент, передаваемый для стандартного метода библиотек `webbrowser."
"open`.\n"
"        Поведение не гарантируется, но зависит от поддержки браузера. "
"Допустимыми\n"
"        значениями являются:\n"
"            2 открывает новую вкладку,\n"
"            1 открывает новое окно,\n"
"            0 открывается в существующем окне.\n"
"        См. документацию `webbrowser.open` для получения подробной "
"информации.\n"
"        "

#: notebook/notebookapp.py:737
msgid "DEPRECATED, use tornado_settings"
msgstr "УСТАРЕЛО - используйте tornado_settings"

#: notebook/notebookapp.py:742
msgid ""
"\n"
"    webapp_settings is deprecated, use tornado_settings.\n"
msgstr ""
"\n"
"    web app_settings устарел, используйте tornado_settings.\n"

#: notebook/notebookapp.py:746
msgid ""
"Supply overrides for the tornado.web.Application that the Jupyter notebook "
"uses."
msgstr ""
"Перекрывает поставки для tornado.web. Приложение, использующее блокнот "
"Jupyter."

#: notebook/notebookapp.py:750
msgid ""
"\n"
"        Set the tornado compression options for websocket connections.\n"
"\n"
"        This value will be returned from :meth:`WebSocketHandler."
"get_compression_options`.\n"
"        None (default) will disable compression.\n"
"        A dict (even an empty one) will enable compression.\n"
"\n"
"        See the tornado docs for WebSocketHandler.get_compression_options "
"for details.\n"
"        "
msgstr ""
"\n"
"        Установите параметры сжатия tornado для соединений websocket.\n"
"\n"
"        Это значение будет возвращено из :meth: 'WebSocketHandler."
"get_compression_options`.\n"
"        None (по умолчанию) отключит сжатие.\n"
"        Диктат (даже пустой) будет включать сжатие.\n"
"\n"
"        Смотрите документы tornado для WebSocketHandler."
"get_compression_options для получения подробной информации.\n"
"        "

#: notebook/notebookapp.py:761
msgid ""
"Supply overrides for terminado. Currently only supports \"shell_command\"."
msgstr ""
"Переопределение поставок для terminado. В настоящее время поддерживается "
"только \"shell_command\"."

#: notebook/notebookapp.py:764
msgid ""
"Extra keyword arguments to pass to `set_secure_cookie`. See tornado's "
"set_secure_cookie docs for details."
msgstr ""
"Дополнительные аргументы ключевого слова для передачи в `set_secure_cookie`. "
"Подробнее см. документацию set_secure_cookie для tornado."

#: notebook/notebookapp.py:768
msgid ""
"Supply SSL options for the tornado HTTPServer.\n"
"            See the tornado docs for details."
msgstr ""
"Предоставьте параметры SSL для HTTPServer tornado.\n"
"            Подробности смотрите в документации tornado."

#: notebook/notebookapp.py:772
msgid "Supply extra arguments that will be passed to Jinja environment."
msgstr "Предоставьте дополнительные аргументы, передаваемые в среду Jinja."

#: notebook/notebookapp.py:776
msgid "Extra variables to supply to jinja templates when rendering."
msgstr ""
"Дополнительные переменные для предоставления шаблонам jinja при рендеринге."

#: notebook/notebookapp.py:812
msgid "DEPRECATED use base_url"
msgstr "УСТАРЕЛО - используйте base_url"

#: notebook/notebookapp.py:816
msgid "base_project_url is deprecated, use base_url"
msgstr "base_project_url устарел - используйте base_url"

#: notebook/notebookapp.py:832
msgid "Path to search for custom.js, css"
msgstr "Путь для поиска custom.js, css"

#: notebook/notebookapp.py:844
msgid ""
"Extra paths to search for serving jinja templates.\n"
"\n"
"        Can be used to override templates from notebook.templates."
msgstr ""
"Дополнительные пути для поиска обслуживающих шаблонов jinja.\n"
"\n"
"        Может использоваться для переопределения шаблонов из notebook."
"templates."

#: notebook/notebookapp.py:855
msgid "extra paths to look for Javascript notebook extensions"
msgstr "дополнительные пути для поиска расширений блокнота Javascript"

#: notebook/notebookapp.py:900
#, python-format
msgid "Using MathJax: %s"
msgstr "Использование MathJax: %s"

#: notebook/notebookapp.py:903
msgid "The MathJax.js configuration file that is to be used."
msgstr "Использовать файл конфигурации MathJax.js."

#: notebook/notebookapp.py:908
#, python-format
msgid "Using MathJax configuration file: %s"
msgstr "Использование файла конфигурации MathJax: %s"

#: notebook/notebookapp.py:914
msgid "The notebook manager class to use."
msgstr "Используемый класс менеджера блокнотов."

#: notebook/notebookapp.py:920
msgid "The kernel manager class to use."
msgstr "Используемый класс менеджера ядра."

#: notebook/notebookapp.py:926
msgid "The session manager class to use."
msgstr "Класс менеджера сеансов для использования."

#: notebook/notebookapp.py:932
msgid "The config manager class to use"
msgstr "Класс менеджера конфигурации для использования"

#: notebook/notebookapp.py:953
msgid "The login handler class to use."
msgstr "Используемый класс обработчика входа в систему."

#: notebook/notebookapp.py:960
msgid "The logout handler class to use."
msgstr "Используемый класс обработчика выхода из системы."

#: notebook/notebookapp.py:964
msgid ""
"Whether to trust or not X-Scheme/X-Forwarded-Proto and X-Real-Ip/X-Forwarded-"
"For headerssent by the upstream reverse proxy. Necessary if the proxy "
"handles SSL"
msgstr ""
"Стоит ли доверять или нет Х-схеме/х-перенаправлению-прото и X-реал-ip/х-"
"перенаправлению-для заголовков, загружаемых по потоку обратного прокси-"
"сервера. Необходимо, если прокси обрабатывает SSL"

#: notebook/notebookapp.py:976
msgid ""
"\n"
"        DISABLED: use %pylab or %matplotlib in the notebook to enable "
"matplotlib.\n"
"        "
msgstr ""
"\n"
"        Отключено: используйте %pylab или %matplotlib в блокноте чтобы "
"включить matplotlib.\n"
"         "

#: notebook/notebookapp.py:988
msgid "Support for specifying --pylab on the command line has been removed."
msgstr "Поддержка указания --pylab в командной строке была удалена."

#: notebook/notebookapp.py:990
msgid "Please use `%pylab{0}` or `%matplotlib{0}` in the notebook itself."
msgstr ""
"Пожалуйста, используйте `%pylab{0}` или `%matplotlib{0}` в самом блокноте."

#: notebook/notebookapp.py:995
msgid "The directory to use for notebooks and kernels."
msgstr "Каталог, используемый для блокнотов и ядер."

#: notebook/notebookapp.py:1018
#, python-format
msgid "No such notebook dir: '%r'"
msgstr "Нет такого блокнота: '%r'"

#: notebook/notebookapp.py:1031
msgid "DEPRECATED use the nbserver_extensions dict instead"
msgstr "УСТАРЕЛО - используйте nbserver_extensions вместо диктата"

#: notebook/notebookapp.py:1036
msgid "server_extensions is deprecated, use nbserver_extensions"
msgstr "server_extensions устарело - используйте nbserver_extensions"

#: notebook/notebookapp.py:1040
msgid ""
"Dict of Python modules to load as notebook server extensions.Entry values "
"can be used to enable and disable the loading ofthe extensions. The "
"extensions will be loaded in alphabetical order."
msgstr ""
"Dict модулей Python для загрузки в качестве серверных расширений блокнотов. "
"Введенные значения можно использовать для включения и отключения загрузки "
"расширений. Расширения будут загружены в алфавитном порядке."

#: notebook/notebookapp.py:1049
msgid "Reraise exceptions encountered loading server extensions?"
msgstr ""
"Повторно поднимаемые исключения, встречающиеся при загрузке серверных "
"расширений?"

#: notebook/notebookapp.py:1052
msgid ""
"(msgs/sec)\n"
"        Maximum rate at which messages can be sent on iopub before they are\n"
"        limited."
msgstr ""
"(сообщ/сек)\n"
"        Максимальная скорость отправки сообщений на iopub, прежде\n"
"         чем они будут ограничены."

#: notebook/notebookapp.py:1056
msgid ""
"(bytes/sec)\n"
"        Maximum rate at which stream output can be sent on iopub before they "
"are\n"
"        limited."
msgstr ""
"(байт/сек)\n"
"        Максимальная скорость отправки потока вывода на iopub, прежде\n"
"         чем он будет ограничен."

#: notebook/notebookapp.py:1060
msgid ""
"(sec) Time window used to \n"
"        check the message and data rate limits."
msgstr ""
"(сек) Окно времени, используемое для \n"
"          проверки ограничений скорости передачи сообщений и данных."

#: notebook/notebookapp.py:1071
#, python-format
msgid "No such file or directory: %s"
msgstr "Нет такого файла или каталога: %s"

#: notebook/notebookapp.py:1141
msgid "Notebook servers are configured to only be run with a password."
msgstr "Серверы блокнотов настроены на работу только с паролем."

#: notebook/notebookapp.py:1142
msgid "Hint: run the following command to set a password"
msgstr "Подсказка: выполните следующую команду, чтобы установить пароль"

#: notebook/notebookapp.py:1143
msgid "\t$ python -m notebook.auth password"
msgstr "\t$ python -m notebook.auth password"

#: notebook/notebookapp.py:1181
#, python-format
msgid "The port %i is already in use, trying another port."
msgstr "Порт %i уже используется, попробуйте другой порт."

#: notebook/notebookapp.py:1184
#, python-format
msgid "Permission to listen on port %i denied"
msgstr "Разрешение на прослушивание порта %i отклонено"

#: notebook/notebookapp.py:1193
msgid ""
"ERROR: the notebook server could not be started because no available port "
"could be found."
msgstr ""
"ОШИБКА: не удалось запустить сервер блокнота, так как не удалось найти "
"доступный порт."

#: notebook/notebookapp.py:1199
msgid "[all ip addresses on your system]"
msgstr "[все ip-адреса в вашей системе]"

#: notebook/notebookapp.py:1223
#, python-format
msgid "Terminals not available (error was %s)"
msgstr "Терминалы недоступны (ошибка: %s)"

#: notebook/notebookapp.py:1259
msgid "interrupted"
msgstr "прервано"

#: notebook/notebookapp.py:1261
msgid "y"
msgstr "д"

#: notebook/notebookapp.py:1262
msgid "n"
msgstr "н"

#: notebook/notebookapp.py:1263
#, python-format
msgid "Shutdown this notebook server (%s/[%s])? "
msgstr "Выключить этот сервер блокнотов (%s/[%s])? "

#: notebook/notebookapp.py:1269
msgid "Shutdown confirmed"
msgstr "Выключение подтверждено"

#: notebook/notebookapp.py:1273
msgid "No answer for 5s:"
msgstr "Нет ответа уже 5с:"

#: notebook/notebookapp.py:1274
msgid "resuming operation..."
msgstr "возобновить работу..."

#: notebook/notebookapp.py:1282
#, python-format
msgid "received signal %s, stopping"
msgstr "получен сигнал %s, остановка"

#: notebook/notebookapp.py:1338
#, python-format
msgid "Error loading server extension %s"
msgstr "Ошибка загрузки расширения сервера %s"

#: notebook/notebookapp.py:1369
#, python-format
msgid "Shutting down %d kernels"
msgstr "Выключить %d ядер"

#: notebook/notebookapp.py:1375
#, python-format
msgid "%d active kernel"
msgid_plural "%d active kernels"
msgstr[0] "%d активное ядро"
msgstr[1] "%d активных ядра"
msgstr[2] "%d активных ядер"

#: notebook/notebookapp.py:1379
#, python-format
msgid ""
"The Jupyter Notebook is running at:\n"
"\r%s"
msgstr ""
"Jupyter Notebook запущен на:\n"
"\r%s"

#: notebook/notebookapp.py:1426
msgid "Running as root is not recommended. Use --allow-root to bypass."
msgstr ""
"Запуск от имени root не рекомендуется. Используйте --allow-root для обхода."

#: notebook/notebookapp.py:1432
msgid ""
"Use Control-C to stop this server and shut down all kernels (twice to skip "
"confirmation)."
msgstr ""
"Используйте Control-C, для остановки этого сервера и выключения всех ядер "
"(дважды, чтобы пропустить подтверждение)."

#: notebook/notebookapp.py:1434
msgid ""
"Welcome to Project Jupyter! Explore the various tools available and their "
"corresponding documentation. If you are interested in contributing to the "
"platform, please visit the communityresources section at http://jupyter.org/"
"community.html."
msgstr ""
"Добро пожаловать в проект Jupyter! Изучите различные доступные инструменты и "
"соответствующую им документацию. Если вы заинтересованы в том, чтобы внести "
"свой вклад в платформу, пожалуйста, посетите раздел ресурсов сообщества по "
"адресу http://jupyter.org/community.html."

#: notebook/notebookapp.py:1445
#, python-format
msgid "No web browser found: %s."
msgstr "Веб-браузер не найден: %s."

#: notebook/notebookapp.py:1450
#, python-format
msgid "%s does not exist"
msgstr "%s не существует"

#: notebook/notebookapp.py:1484
msgid "Interrupted..."
msgstr "Прерывание..."

#: notebook/services/contents/filemanager.py:506
#, python-format
msgid "Serving notebooks from local directory: %s"
msgstr "Обслуживание блокнотов из локального каталога: %s"

#: notebook/services/contents/manager.py:68
msgid "Untitled"
msgstr "Без названия"
