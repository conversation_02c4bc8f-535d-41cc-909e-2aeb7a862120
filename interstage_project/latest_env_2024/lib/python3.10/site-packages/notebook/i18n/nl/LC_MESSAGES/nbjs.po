# Translations template for Jupyter.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-06-27 14:04-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

#: notebook/static/base/js/dialog.js:161
msgid "Manually edit the JSON below to manipulate the metadata for this cell."
msgstr ""
"Bewerk de JSON eronder handmatig om de metagegevens voor deze cel te "
"manipuleren."

#: notebook/static/base/js/dialog.js:163
msgid ""
"Manually edit the JSON below to manipulate the metadata for this notebook."
msgstr ""
"Bewerk de JSON eronder handmatig om de metagegevens voor dit notebook "
"te manipuleren."

#: notebook/static/base/js/dialog.js:165
msgid ""
" We recommend putting custom metadata attributes in an appropriately named "
"substructure, so they don't conflict with those of others."
msgstr ""
" We raden u aan aangepaste metagegevenskenmerken in een op de juiste naam "
"genoemde substructuur te plaatsen, zodat ze niet in strijd zijn met die van "
"anderen."

#: notebook/static/base/js/dialog.js:180
msgid "Edit the metadata"
msgstr "De metagegevens bewerken"

#: notebook/static/base/js/dialog.js:202
msgid "Edit Notebook Metadata"
msgstr "Metagegevens van notebook bewerken"

#: notebook/static/base/js/dialog.js:204
msgid "Edit Cell Metadata"
msgstr "Celmetagegevens bewerken"

#: notebook/static/base/js/dialog.js:208
#: notebook/static/notebook/js/notebook.js:475
#: notebook/static/notebook/js/savewidget.js:71
#: notebook/static/tree/js/notebooklist.js:859
#: notebook/static/tree/js/notebooklist.js:1418
msgid "Cancel"
msgstr "Annuleren"

#: notebook/static/base/js/dialog.js:208
msgid "Edit"
msgstr "Bewerken"

#: notebook/static/base/js/dialog.js:208
#: notebook/static/notebook/js/kernelselector.js:278
#: notebook/static/notebook/js/mathjaxutils.js:42
#: notebook/static/notebook/js/notebook.js:469
#: notebook/static/notebook/js/notificationarea.js:187
#: notebook/static/notebook/js/savewidget.js:71
#: notebook/static/tree/js/newnotebook.js:97
#: notebook/static/tree/js/notebooklist.js:859
msgid "OK"
msgstr "OK"

#: notebook/static/base/js/dialog.js:208
msgid "Apply"
msgstr "Toepassen"

#: notebook/static/base/js/dialog.js:225
msgid "WARNING: Could not save invalid JSON."
msgstr "WAARSCHUWING: Kan ongeldige JSON niet opslaan."

#: notebook/static/base/js/dialog.js:247
msgid "There are no attachments for this cell."
msgstr "Er zijn geen bijlagen voor deze cel."

#: notebook/static/base/js/dialog.js:250
msgid "Current cell attachments"
msgstr "Huidige celbijlagen"

#: notebook/static/base/js/dialog.js:259
#: notebook/static/notebook/js/celltoolbarpresets/attachments.js:46
msgid "Attachments"
msgstr "Bijlagen"

#: notebook/static/base/js/dialog.js:283
msgid "Restore"
msgstr "Herstellen"

#: notebook/static/base/js/dialog.js:293
#: notebook/static/tree/js/notebooklist.js:1018
msgid "Delete"
msgstr "Verwijderen"

#: notebook/static/base/js/dialog.js:342 notebook/static/base/js/dialog.js:386
msgid "Edit attachments"
msgstr "Bijlagen bewerken"

#: notebook/static/base/js/dialog.js:348
msgid "Edit Notebook Attachments"
msgstr "Notebook-bijlagen bewerken"

#: notebook/static/base/js/dialog.js:350
msgid "Edit Cell Attachments"
msgstr "Celbijlagen bewerken"

#: notebook/static/base/js/dialog.js:373
msgid "Select a file to insert."
msgstr "Selecteer een bestand dat u wilt invoegen."

#: notebook/static/base/js/dialog.js:399
msgid "Select a file"
msgstr "Een bestand selecteren"

#: notebook/static/notebook/js/about.js:14
msgid "You are using Jupyter notebook."
msgstr "U gebruikt Jupyter notebook."

#: notebook/static/notebook/js/about.js:16
msgid "The version of the notebook server is: "
msgstr "De versie van de notebookserver is: "

#: notebook/static/notebook/js/about.js:22
msgid "The server is running on this version of Python:"
msgstr "De server draait op deze versie van Python:"

#: notebook/static/notebook/js/about.js:25
msgid "Waiting for kernel to be available..."
msgstr "Wachten tot kernel beschikbaar is..."

#: notebook/static/notebook/js/about.js:27
msgid "Server Information:"
msgstr "Servergegevens:"

#: notebook/static/notebook/js/about.js:29
msgid "Current Kernel Information:"
msgstr "Informatie van huidige kernel:"

#: notebook/static/notebook/js/about.js:32
msgid "Could not access sys_info variable for version information."
msgstr ""
"Kon geen toegang krijgen tot sys_info variabele voor versie-informatie."

#: notebook/static/notebook/js/about.js:34
msgid "Cannot find sys_info!"
msgstr "Ik kan sys_info niet vinden!"

#: notebook/static/notebook/js/about.js:38
msgid "About Jupyter Notebook"
msgstr "Over Jupyter Notebook"

#: notebook/static/notebook/js/about.js:47
msgid "unable to contact kernel"
msgstr "kan de kernel niet bereiken"

#: notebook/static/notebook/js/actions.js:69
msgid "toggle rtl layout"
msgstr "rtl-indeling in- en uitschakelen"

#: notebook/static/notebook/js/actions.js:70
msgid ""
"Toggle the screen directionality between left-to-right and right-to-left"
msgstr ""
"De richting van het scherm schakelen tussen links-naar-rechts en rechts-"
"naar-links"

#: notebook/static/notebook/js/actions.js:76
msgid "edit command mode keyboard shortcuts"
msgstr "sneltoetsen in de opdrachtmodus bewerken"

#: notebook/static/notebook/js/actions.js:77
msgid "Open a dialog to edit the command mode keyboard shortcuts"
msgstr ""
"Een dialoogvenster openen om sneltoetsen in de opdrachtmodus te bewerken"

#: notebook/static/notebook/js/actions.js:97
msgid "restart kernel"
msgstr "kernel herstarten"

#: notebook/static/notebook/js/actions.js:98
msgid "restart the kernel (no confirmation dialog)"
msgstr "de kernel herstarten (geen bevestigingsdialoogvenster)"

#: notebook/static/notebook/js/actions.js:106
msgid "confirm restart kernel"
msgstr "bevestig herstart van kernel"

#: notebook/static/notebook/js/actions.js:107
msgid "restart the kernel (with dialog)"
msgstr "de kernel herstarten (met dialoogvenster)"

#: notebook/static/notebook/js/actions.js:113
msgid "restart kernel and run all cells"
msgstr "kernel herstarten en alle cellen uitvoeren"

#: notebook/static/notebook/js/actions.js:114
msgid ""
"restart the kernel, then re-run the whole notebook (no confirmation dialog)"
msgstr ""
"herstart de kernel en voer vervolgens het hele notebook uit "
"(geen bevestigingsdialoogvenster)"

#: notebook/static/notebook/js/actions.js:120
msgid "confirm restart kernel and run all cells"
msgstr "bevestig herstartern kernel en cellen uitvoeren"

#: notebook/static/notebook/js/actions.js:121
msgid "restart the kernel, then re-run the whole notebook (with dialog)"
msgstr ""
"herstart de kernel en voer vervolgens het hele notebook uit "
"(met dialoogvenster)"

#: notebook/static/notebook/js/actions.js:127
msgid "restart kernel and clear output"
msgstr "kernel herstarten en de uitvoer wissen"

#: notebook/static/notebook/js/actions.js:128
msgid "restart the kernel and clear all output (no confirmation dialog)"
msgstr ""
"herstart de kernel en schakel alle uitvoer uit (geen "
"bevestigingsdialoogvenster)"

#: notebook/static/notebook/js/actions.js:134
msgid "confirm restart kernel and clear output"
msgstr "bevestigen herstart kernel en verwijder uitvoer"

#: notebook/static/notebook/js/actions.js:135
msgid "restart the kernel and clear all output (with dialog)"
msgstr ""
"herstart de kernel en verwijder alle uitvoer (met dialoogvenster)"

#: notebook/static/notebook/js/actions.js:142
#: notebook/static/notebook/js/actions.js:143
msgid "interrupt the kernel"
msgstr "de kernel onderbreken"

#: notebook/static/notebook/js/actions.js:150
msgid "run cell and select next"
msgstr "cel uitvoeren en volgende selecteren"

#: notebook/static/notebook/js/actions.js:152
msgid "run cell, select below"
msgstr "cel uitvoeren en de cel eronder selecteren"

#: notebook/static/notebook/js/actions.js:159
#: notebook/static/notebook/js/actions.js:160
msgid "run selected cells"
msgstr "geselecteerde cellen uitvoeren"

#: notebook/static/notebook/js/actions.js:167
#: notebook/static/notebook/js/actions.js:168
msgid "run cell and insert below"
msgstr "cel uitvoeren en voeg een cel toe"

#: notebook/static/notebook/js/actions.js:175
#: notebook/static/notebook/js/actions.js:176
msgid "run all cells"
msgstr "alle cellen uitvoeren"

#: notebook/static/notebook/js/actions.js:183
#: notebook/static/notebook/js/actions.js:184
msgid "run all cells above"
msgstr "alle cellen erboven uitvoeren"

#: notebook/static/notebook/js/actions.js:190
#: notebook/static/notebook/js/actions.js:191
msgid "run all cells below"
msgstr "alle cellen eronder uitvoeren"

#: notebook/static/notebook/js/actions.js:197
#: notebook/static/notebook/js/actions.js:198
msgid "enter command mode"
msgstr "ingaan op de opdrachtmodus"

#: notebook/static/notebook/js/actions.js:205
#: notebook/static/notebook/js/actions.js:206
msgid "insert image"
msgstr "afbeelding invoegen"

#: notebook/static/notebook/js/actions.js:213
#: notebook/static/notebook/js/actions.js:214
msgid "cut cell attachments"
msgstr "celbijlagen knippen"

#: notebook/static/notebook/js/actions.js:221
#: notebook/static/notebook/js/actions.js:222
msgid "copy cell attachments"
msgstr "celbijlagen kopiëren"

#: notebook/static/notebook/js/actions.js:229
#: notebook/static/notebook/js/actions.js:230
msgid "paste cell attachments"
msgstr "celbijlagen plakken"

#: notebook/static/notebook/js/actions.js:237
#: notebook/static/notebook/js/actions.js:238
msgid "split cell at cursor"
msgstr "cel splitsen bij cursor"

#: notebook/static/notebook/js/actions.js:245
#: notebook/static/notebook/js/actions.js:246
msgid "enter edit mode"
msgstr "activeer bewerkingsmodus"

#: notebook/static/notebook/js/actions.js:253
msgid "select previous cell"
msgstr "vorige cel selecteren"

#: notebook/static/notebook/js/actions.js:254
msgid "select cell above"
msgstr "cel erboven selecteren"

#: notebook/static/notebook/js/actions.js:265
msgid "select next cell"
msgstr "volgende cel selecteren"

#: notebook/static/notebook/js/actions.js:266
msgid "select cell below"
msgstr "cel eronder selecteren"

#: notebook/static/notebook/js/actions.js:277
msgid "extend selection above"
msgstr "selectie naar boven uitbreiden"

#: notebook/static/notebook/js/actions.js:278
msgid "extend selected cells above"
msgstr "geselecteerde cellen naar boven uitbreiden"

#: notebook/static/notebook/js/actions.js:289
msgid "extend selection below"
msgstr "selectie naar onder uitbreiden"

#: notebook/static/notebook/js/actions.js:290
msgid "extend selected cells below"
msgstr "geselecteerde cellen naar onder uitbreiden"

#: notebook/static/notebook/js/actions.js:301
#: notebook/static/notebook/js/actions.js:302
msgid "cut selected cells"
msgstr "geselecteerde cellen knippen"

#: notebook/static/notebook/js/actions.js:312
#: notebook/static/notebook/js/actions.js:313
msgid "copy selected cells"
msgstr "geselecteerde cellen kopiëren"

#: notebook/static/notebook/js/actions.js:327
#: notebook/static/notebook/js/actions.js:328
msgid "paste cells above"
msgstr "cellen erboven plakken"

#: notebook/static/notebook/js/actions.js:335
#: notebook/static/notebook/js/actions.js:336
msgid "paste cells below"
msgstr "cellen eronder plakken"

#: notebook/static/notebook/js/actions.js:344
#: notebook/static/notebook/js/actions.js:345
msgid "insert cell above"
msgstr "cel erboven invoegen"

#: notebook/static/notebook/js/actions.js:354
#: notebook/static/notebook/js/actions.js:355
msgid "insert cell below"
msgstr "cel eronder invoegen"

#: notebook/static/notebook/js/actions.js:365
#: notebook/static/notebook/js/actions.js:366
msgid "change cell to code"
msgstr "cel wijzigen naar code"

#: notebook/static/notebook/js/actions.js:373
#: notebook/static/notebook/js/actions.js:374
msgid "change cell to markdown"
msgstr "cel wijzigen naar markdown"

#: notebook/static/notebook/js/actions.js:381
#: notebook/static/notebook/js/actions.js:382
msgid "change cell to raw"
msgstr "cel wijzigen naar raw"

#: notebook/static/notebook/js/actions.js:389
#: notebook/static/notebook/js/actions.js:390
msgid "change cell to heading 1"
msgstr "cel wijzigen naar header 1"

#: notebook/static/notebook/js/actions.js:397
#: notebook/static/notebook/js/actions.js:398
msgid "change cell to heading 2"
msgstr "cel wijzigen naar header 2"

#: notebook/static/notebook/js/actions.js:405
#: notebook/static/notebook/js/actions.js:406
msgid "change cell to heading 3"
msgstr "cel wijzigen naar header 3"

#: notebook/static/notebook/js/actions.js:413
#: notebook/static/notebook/js/actions.js:414
msgid "change cell to heading 4"
msgstr "cel wijzigen naar header 4"

#: notebook/static/notebook/js/actions.js:421
#: notebook/static/notebook/js/actions.js:422
msgid "change cell to heading 5"
msgstr "cel wijzigen naar header 5"

#: notebook/static/notebook/js/actions.js:429
#: notebook/static/notebook/js/actions.js:430
msgid "change cell to heading 6"
msgstr "cel wijzigen naar header 6"

#: notebook/static/notebook/js/actions.js:437
msgid "toggle cell output"
msgstr "celuitvoer in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:438
msgid "toggle output of selected cells"
msgstr "uitvoer van geselecteerde cellen in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:445
msgid "toggle cell scrolling"
msgstr "scrollen van cellen in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:446
msgid "toggle output scrolling of selected cells"
msgstr "scrollen van uitvoer van geselecteerde cellen in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:453
msgid "clear cell output"
msgstr "verwijder celuitvoer"

#: notebook/static/notebook/js/actions.js:454
msgid "clear output of selected cells"
msgstr "verwijder uitvoer van geselecteerde cellen"

#: notebook/static/notebook/js/actions.js:460
msgid "move cells down"
msgstr "cellen naar beneden verplaatsen"

#: notebook/static/notebook/js/actions.js:461
msgid "move selected cells down"
msgstr "geselecteerde cellen naar beneden verplaatsen"

#: notebook/static/notebook/js/actions.js:469
msgid "move cells up"
msgstr "cellen omhoog verplaatsen"

#: notebook/static/notebook/js/actions.js:470
msgid "move selected cells up"
msgstr "geselecteerde cellen omhoog verplaatsen"

#: notebook/static/notebook/js/actions.js:478
#: notebook/static/notebook/js/actions.js:479
msgid "toggle line numbers"
msgstr "regelnummers in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:486
#: notebook/static/notebook/js/actions.js:487
msgid "show keyboard shortcuts"
msgstr "sneltoetsen weergeven"

#: notebook/static/notebook/js/actions.js:494
msgid "delete cells"
msgstr "cellen verwijderen"

#: notebook/static/notebook/js/actions.js:495
msgid "delete selected cells"
msgstr "geselecteerde cellen verwijderen"

#: notebook/static/notebook/js/actions.js:502
#: notebook/static/notebook/js/actions.js:503
msgid "undo cell deletion"
msgstr "celverwijdering ongedaan maken"

#: notebook/static/notebook/js/actions.js:512
msgid "merge cell with previous cell"
msgstr "cel samenvoegen met vorige cel"

#: notebook/static/notebook/js/actions.js:513
msgid "merge cell above"
msgstr "cel erboven samenvoegen"

#: notebook/static/notebook/js/actions.js:519
msgid "merge cell with next cell"
msgstr "cel samenvoegen met volgende cel"

#: notebook/static/notebook/js/actions.js:520
msgid "merge cell below"
msgstr "cel eronder samenvoegen"

#: notebook/static/notebook/js/actions.js:527
#: notebook/static/notebook/js/actions.js:528
msgid "merge selected cells"
msgstr "geselecteerde cellen samenvoegen"

#: notebook/static/notebook/js/actions.js:535
msgid "merge cells"
msgstr "cellen samenvoegen"

#: notebook/static/notebook/js/actions.js:536
msgid ""
"merge selected cells, or current cell with cell below if only one cell is "
"selected"
msgstr ""
"geselecteerde cellen of huidige cel samenvoegen met cel eronder als "
"slechts één cel is geselecteerd"

#: notebook/static/notebook/js/actions.js:549
msgid "show command pallette"
msgstr "opdrachtpallette weergeven"

#: notebook/static/notebook/js/actions.js:550
msgid "open the command palette"
msgstr "opdrachtpallette openen"

#: notebook/static/notebook/js/actions.js:557
msgid "toggle all line numbers"
msgstr "alle regelnummers in- en uitschakelen"

#: notebook/static/notebook/js/actions.js:558
msgid "toggles line numbers in all cells, and persist the setting"
msgstr "lijnnummers in alle cellen in- of uitschakelen en de instelling aanhouden"

#: notebook/static/notebook/js/actions.js:569
msgid "show all line numbers"
msgstr "alle regelnummers weergeven"

#: notebook/static/notebook/js/actions.js:570
msgid "show line numbers in all cells, and persist the setting"
msgstr "lijnnummers in alle cellen weergeven en de instelling aanhouden"

#: notebook/static/notebook/js/actions.js:579
msgid "hide all line numbers"
msgstr "alle regelnummers verbergen"

#: notebook/static/notebook/js/actions.js:580
msgid "hide line numbers in all cells, and persist the setting"
msgstr "regelnummers in alle cellen verbergen en de instelling aanhouden"

#: notebook/static/notebook/js/actions.js:589
msgid "toggle header"
msgstr "koptekst in- en uitschakelen"

#: notebook/static/notebook/js/actions.js:590
msgid "switch between showing and hiding the header"
msgstr "schakelen tussen het weergeven en verbergen van de koptekst"

#: notebook/static/notebook/js/actions.js:605
#: notebook/static/notebook/js/actions.js:606
msgid "show the header"
msgstr "de koptekst weergeven"

#: notebook/static/notebook/js/actions.js:615
#: notebook/static/notebook/js/actions.js:616
msgid "hide the header"
msgstr "de koptekst verbergen"

#: notebook/static/notebook/js/actions.js:646
msgid "toggle toolbar"
msgstr "werkbalk schakelen"

#: notebook/static/notebook/js/actions.js:647
msgid "switch between showing and hiding the toolbar"
msgstr "schakelen tussen het weergeven en verbergen van de werkbalk"

#: notebook/static/notebook/js/actions.js:660
#: notebook/static/notebook/js/actions.js:661
msgid "show the toolbar"
msgstr "de werkbalk weergeven"

#: notebook/static/notebook/js/actions.js:669
#: notebook/static/notebook/js/actions.js:670
msgid "hide the toolbar"
msgstr "de werkbalk verbergen"

#: notebook/static/notebook/js/actions.js:678
#: notebook/static/notebook/js/actions.js:679
msgid "close the pager"
msgstr "de pager sluiten"

#: notebook/static/notebook/js/actions.js:704
msgid "ignore"
msgstr "Negeren"

#: notebook/static/notebook/js/actions.js:710
#: notebook/static/notebook/js/actions.js:711
msgid "move cursor up"
msgstr "cursor omhoog verplaatsen"

#: notebook/static/notebook/js/actions.js:731
#: notebook/static/notebook/js/actions.js:732
msgid "move cursor down"
msgstr "cursor omlaag verplaatsen"

#: notebook/static/notebook/js/actions.js:750
#: notebook/static/notebook/js/actions.js:751
msgid "scroll notebook down"
msgstr "scroll notebook omlaag"

#: notebook/static/notebook/js/actions.js:760
#: notebook/static/notebook/js/actions.js:761
msgid "scroll notebook up"
msgstr "scroll notebook omhoog"

#: notebook/static/notebook/js/actions.js:770
msgid "scroll cell center"
msgstr "scroll cel naar het midden"

#: notebook/static/notebook/js/actions.js:771
msgid "Scroll the current cell to the center"
msgstr "De huidige cel naar het midden scrollen"

#: notebook/static/notebook/js/actions.js:781
msgid "scroll cell top"
msgstr "scroll cel naar boven"

#: notebook/static/notebook/js/actions.js:782
msgid "Scroll the current cell to the top"
msgstr "De huidige cel volledig naar boven scrollen"

#: notebook/static/notebook/js/actions.js:792
msgid "duplicate notebook"
msgstr "dupliceer notebook"

#: notebook/static/notebook/js/actions.js:793
msgid "Create and open a copy of the current notebook"
msgstr "Een kopie van het huidige notebook maken en openen"

#: notebook/static/notebook/js/actions.js:799
msgid "trust notebook"
msgstr "vertrouw notebook"

#: notebook/static/notebook/js/actions.js:800
msgid "Trust the current notebook"
msgstr "Het huidige notebook vertrouwen"

#: notebook/static/notebook/js/actions.js:806
msgid "rename notebook"
msgstr "naam van notebook wijzigen"

#: notebook/static/notebook/js/actions.js:807
msgid "Rename the current notebook"
msgstr "De naam van het huidige notebook wijzigen"

#: notebook/static/notebook/js/actions.js:813
msgid "toggle all cells output collapsed"
msgstr "alle samengevouwen celuitvoer in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:814
msgid "Toggle the hidden state of all output areas"
msgstr "De zichtbaarheid status van alle uitvoergebieden in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:820
msgid "toggle all cells output scrolled"
msgstr "alle cellenuitvoer in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:821
msgid "Toggle the scrolling state of all output areas"
msgstr "De scroll-status van alle uitvoergebieden in- of uitschakelen"

#: notebook/static/notebook/js/actions.js:828
msgid "clear all cells output"
msgstr "alle cellenuitvoer wissen"

#: notebook/static/notebook/js/actions.js:829
msgid "Clear the content of all the outputs"
msgstr "De inhoud van alle celuitvoer wissen"

#: notebook/static/notebook/js/actions.js:835
msgid "save notebook"
msgstr "notebook opslaan"

#: notebook/static/notebook/js/actions.js:836
msgid "Save and Checkpoint"
msgstr "Opslaan en checkpoint"

#: notebook/static/notebook/js/cell.js:79
msgid "Warning: accessing Cell.cm_config directly is deprecated."
msgstr ""
"Waarschuwing: rechtstreeks toegang tot Cell.cm_config wordt afgeschaft."

#: notebook/static/notebook/js/cell.js:763
#, python-format
msgid "Unrecognized cell type: %s"
msgstr "Niet-herkende celtype: %s"

#: notebook/static/notebook/js/cell.js:777
msgid "Unrecognized cell type"
msgstr "Niet-herkende celtype"

#: notebook/static/notebook/js/celltoolbar.js:296
#, python-format
msgid "Error in cell toolbar callback %s"
msgstr "Fout in callback-functie van de celwerkbalk"

#: notebook/static/notebook/js/clipboard.js:53
#, python-format
msgid "Clipboard types: %s"
msgstr "Klembordtypen: %s"

#: notebook/static/notebook/js/clipboard.js:96
msgid "Dialog for paste from system clipboard"
msgstr "Dialoogvenster voor plakken van het klembord van het systeem"

#: notebook/static/notebook/js/clipboard.js:109
msgid "Ctrl-V"
msgstr "Ctrl-V"

#: notebook/static/notebook/js/clipboard.js:111
msgid "Cmd-V"
msgstr "Cmd-V"

#: notebook/static/notebook/js/clipboard.js:113
#, python-format
msgid "Press %s again to paste"
msgstr "Druk nogmaals op %s om te plakken"

#: notebook/static/notebook/js/clipboard.js:116
msgid "Why is this needed? "
msgstr "Waarom is dit nodig? "

#: notebook/static/notebook/js/clipboard.js:118
msgid "We can't get paste events in this browser without a text box. "
msgstr ""
"We kunnen niet plakken in deze browser zonder tekstvak. "

#: notebook/static/notebook/js/clipboard.js:119
msgid "There's an invisible text box focused in this dialog."
msgstr "Er is een onzichtbaar tekstvak geopend in dit dialoogvenster."

#: notebook/static/notebook/js/clipboard.js:125
#, python-format
msgid "%s to paste"
msgstr "%s plakken"

#: notebook/static/notebook/js/codecell.js:310
msgid "Can't execute cell since kernel is not set."
msgstr "Kan geen cel uitvoeren omdat kernel niet is ingesteld."

#: notebook/static/notebook/js/codecell.js:472
msgid "In"
msgstr "In"

#: notebook/static/notebook/js/kernelselector.js:269
#, python-format
msgid "Could not find a kernel matching %s. Please select a kernel:"
msgstr "Kon geen kernel vinden die overeenkomt met %s. Selecteer een kernel:"

#: notebook/static/notebook/js/kernelselector.js:278
msgid "Continue Without Kernel"
msgstr "Doorgaan zonder kernel"

#: notebook/static/notebook/js/kernelselector.js:278
msgid "Set Kernel"
msgstr "Kernel instellen"

#: notebook/static/notebook/js/kernelselector.js:281
msgid "Kernel not found"
msgstr "Kernel niet gevonden"

#: notebook/static/notebook/js/kernelselector.js:319
#: notebook/static/tree/js/newnotebook.js:99
msgid "Creating Notebook Failed"
msgstr "Notebook maken is mislukt"

#: notebook/static/notebook/js/kernelselector.js:320
#: notebook/static/tree/js/notebooklist.js:1360
#, python-format
msgid "The error was: %s"
msgstr "De fout was: %s"

#: notebook/static/notebook/js/maintoolbar.js:54
msgid "Run"
msgstr "Uitvoeren"

#: notebook/static/notebook/js/maintoolbar.js:76
msgid "Code"
msgstr "Code"

#: notebook/static/notebook/js/maintoolbar.js:77
msgid "Markdown"
msgstr "Markdown"

#: notebook/static/notebook/js/maintoolbar.js:78
msgid "Raw NBConvert"
msgstr "Raw NBConvert"

#: notebook/static/notebook/js/maintoolbar.js:79
msgid "Heading"
msgstr "Headers"

#: notebook/static/notebook/js/maintoolbar.js:115
msgid "unrecognized cell type:"
msgstr "niet-herkende celtype:"

#: notebook/static/notebook/js/mathjaxutils.js:45
#, python-format
msgid "Failed to retrieve MathJax from '%s'"
msgstr "Kan MathJax niet ophalen uit '%s'"

#: notebook/static/notebook/js/mathjaxutils.js:47
msgid "Math/LaTeX rendering will be disabled."
msgstr "Wiskunde/LaTeX-rendering wordt uitgeschakeld."

#: notebook/static/notebook/js/menubar.js:220
msgid "Trusted Notebook"
msgstr "Vertrouwd notebook"

#: notebook/static/notebook/js/menubar.js:226
msgid "Trust Notebook"
msgstr "Notebook vertrouwen"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:16
#: notebook/static/notebook/js/menubar.js:383
msgid "None"
msgstr "Geen"

#: notebook/static/notebook/js/menubar.js:406
msgid "No checkpoints"
msgstr "Geen checkpoints"

#: notebook/static/notebook/js/menubar.js:465
msgid "Opens in a new window"
msgstr "Opent in een nieuw venster"

#: notebook/static/notebook/js/notebook.js:431
msgid "Autosave in progress, latest changes may be lost."
msgstr ""
"Bezig met automatisch opslaan, de laatste wijzigingen kunnen verloren "
"gaan."

#: notebook/static/notebook/js/notebook.js:433
msgid "Unsaved changes will be lost."
msgstr "Niet-opgeslagen wijzigingen gaan verloren."

#: notebook/static/notebook/js/notebook.js:438
msgid "The Kernel is busy, outputs may be lost."
msgstr "De Kernel is bezet, uitvoeren kunnen verloren gaan."

#: notebook/static/notebook/js/notebook.js:461
msgid "This notebook is version %1$s, but we only fully support up to %2$s."
msgstr ""
"Deze notebook is versie %1$s, maar we ondersteunen alleen volledig tot %2$s."

#: notebook/static/notebook/js/notebook.js:463
msgid ""
"You can still work with this notebook, but cell and output types introduced "
"in later notebook versions will not be available."
msgstr ""
"U kunt nog steeds met deze notebook werken, maar cel- en uitvoertypen die in "
"latere notebookversies zijn geïntroduceerd, zijn niet beschikbaar."

#: notebook/static/notebook/js/notebook.js:470
msgid "Restart and Run All Cells"
msgstr "Herstarten en alle cellen uitvoeren"

#: notebook/static/notebook/js/notebook.js:471
msgid "Restart and Clear All Outputs"
msgstr "Herstarten en alle uitvoer wissen"

#: notebook/static/notebook/js/notebook.js:472
msgid "Restart"
msgstr "Herstarten"

#: notebook/static/notebook/js/notebook.js:473
msgid "Continue Running"
msgstr "Doorgaan met uitvoeren"

#: notebook/static/notebook/js/notebook.js:474
msgid "Reload"
msgstr "Reload"

#: notebook/static/notebook/js/notebook.js:476
msgid "Overwrite"
msgstr "Overschrijven"

#: notebook/static/notebook/js/notebook.js:477
msgid "Trust"
msgstr "Vertrouwen"

#: notebook/static/notebook/js/notebook.js:478
msgid "Revert"
msgstr "Terugkeren"

#: notebook/static/notebook/js/notebook.js:483
msgid "Newer Notebook"
msgstr "Nieuwer notebook"

#: notebook/static/notebook/js/notebook.js:1548
msgid "Use markdown headings"
msgstr "Markdown headers gebruiken"

#: notebook/static/notebook/js/notebook.js:1550
msgid ""
"Jupyter no longer uses special heading cells. Instead, write your headings "
"in Markdown cells using # characters:"
msgstr ""
"Jupyter maakt geen gebruik meer van speciale headercellen. Schrijf in plaats "
"daarvan uw headers in Markdown-cellen met # tekens:"

#: notebook/static/notebook/js/notebook.js:1553
msgid "## This is a level 2 heading"
msgstr "## Dit is een niveau 2 header"

#: notebook/static/notebook/js/notebook.js:2248
msgid "Restart kernel and re-run the whole notebook?"
msgstr "Kernel herstarten en het hele notebook opnieuw uitvoeren?"

#: notebook/static/notebook/js/notebook.js:2250
msgid ""
"Are you sure you want to restart the current kernel and re-execute the whole"
" notebook?  All variables and outputs will be lost."
msgstr ""
"Weet u zeker dat u de huidige kernel wilt herstarten en het hele "
"notebook opnieuw wilt uitvoeren?  Alle variabelen en uitgangen gaan "
"verloren."

#: notebook/static/notebook/js/notebook.js:2275
msgid "Restart kernel and clear all output?"
msgstr "Herstart kernel en verwijder alle output?"

#: notebook/static/notebook/js/notebook.js:2277
msgid ""
"Do you want to restart the current kernel and clear all output?  All "
"variables and outputs will be lost."
msgstr ""
"Wilt u de huidige kernel herstarten en alle uitvoer wissen?  Alle "
"variabelen en uitgangen gaan verloren."

#: notebook/static/notebook/js/notebook.js:2322
msgid "Restart kernel?"
msgstr "Kernel herstarten?"

#: notebook/static/notebook/js/notebook.js:2324
msgid ""
"Do you want to restart the current kernel?  All variables will be lost."
msgstr ""
"Wilt u de huidige kernel herstarten?  Alle variabelen gaan verloren."

#: notebook/static/notebook/js/notebook.js:2320
msgid "Shutdown kernel?"
msgstr "Kernel afsluiten?"

#: notebook/static/notebook/js/notebook.js:2322
msgid ""
"Do you want to shutdown the current kernel?  All variables will be lost."
msgstr "Wilt u de huidige kernel afsluiten?  Alle variabelen gaan verloren."

#: notebook/static/notebook/js/notebook.js:2734
msgid "Notebook changed"
msgstr "Notebook gewijzigd"

#: notebook/static/notebook/js/notebook.js:2735
msgid ""
"The notebook file has changed on disk since the last time we opened or saved"
" it. Do you want to overwrite the file on disk with the version open here, "
"or load the version on disk (reload the page) ?"
msgstr ""
"Het notebookbestand op de schijf is gewijzigd sinds de laatste keer dat het "
"geopend of opgeslagen is. Wilt u het bestand op schijf overschrijven"
" met de versie die hier wordt geopend, of de versie op schijf laden (de "
"pagina opnieuw laden) ?"

#: notebook/static/notebook/js/notebook.js:2782
#: notebook/static/notebook/js/notebook.js:2990
msgid "Notebook validation failed"
msgstr "Notebook validatie is mislukt"

#: notebook/static/notebook/js/notebook.js:2785
msgid ""
"The save operation succeeded, but the notebook does not appear to be valid. "
"The validation error was:"
msgstr ""
"De opslagbewerking is geslaagd, maar het notebook lijkt niet geldig te "
"zijn. De validatiefout was:"

#: notebook/static/notebook/js/notebook.js:2836
msgid ""
"A trusted Jupyter notebook may execute hidden malicious code when you open "
"it. Selecting trust will immediately reload this notebook in a trusted "
"state. For more information, see the Jupyter security documentation: "
msgstr ""
"Een vertrouwd Jupyter-notebook kan verborgen schadelijke code uitvoeren "
"wanneer u het opent. Als u vertrouwensstatus selecteert, wordt dit "
"notebook onmiddellijk opnieuw geladen in een vertrouwde status. Zie voor "
"meer informatie de beveiligingsdocumentatie van Jupyter: "

#: notebook/static/notebook/js/notebook.js:2840
msgid "here"
msgstr "hier"

#: notebook/static/notebook/js/notebook.js:2848
msgid "Trust this notebook?"
msgstr "Vertrouw je dit notebook?"

#: notebook/static/notebook/js/notebook.js:2981
msgid "Notebook failed to load"
msgstr "Notebook kan niet worden geladen"

#: notebook/static/notebook/js/notebook.js:2983
msgid "The error was: "
msgstr "De fout was: "

#: notebook/static/notebook/js/notebook.js:2987
msgid "See the error console for details."
msgstr "Zie de foutconsole voor meer informatie."

#: notebook/static/notebook/js/notebook.js:2995
msgid "The notebook also failed validation:"
msgstr "De notebook validatie is ook mislukt:"

#: notebook/static/notebook/js/notebook.js:2997
msgid ""
"An invalid notebook may not function properly. The validation error was:"
msgstr ""
"Een ongeldig notebook werkt mogelijk niet goed. De validatiefout was:"

#: notebook/static/notebook/js/notebook.js:3036
#, python-format
msgid ""
"This notebook has been converted from an older notebook format to the "
"current notebook format v(%s)."
msgstr ""
"Deze notebook is geconverteerd van een oudere notebookindeling naar de "
"huidige notebookindeling v(%s)."

#: notebook/static/notebook/js/notebook.js:3038
#, python-format
msgid ""
"This notebook has been converted from a newer notebook format to the current"
" notebook format v(%s)."
msgstr ""
"Deze notebook is geconverteerd van een nieuwere notebookindeling naar de "
"huidige notebookindeling v(%s)."

#: notebook/static/notebook/js/notebook.js:3046
msgid ""
"The next time you save this notebook, the current notebook format will be "
"used."
msgstr ""
"De volgende keer dat u dit notebook opslaat, wordt de huidige "
"notebookindeling gebruikt."

#: notebook/static/notebook/js/notebook.js:3051
msgid "Older versions of Jupyter may not be able to read the new format."
msgstr ""
"Oudere versies van Jupyter kunnen het nieuwe formaat mogelijk niet lezen."

#: notebook/static/notebook/js/notebook.js:3053
msgid "Some features of the original notebook may not be available."
msgstr ""
"Sommige functies van de originele notebook zijn mogelijk niet beschikbaar."

#: notebook/static/notebook/js/notebook.js:3056
msgid ""
"To preserve the original version, close the notebook without saving it."
msgstr ""
"Als u de oorspronkelijke versie wilt behouden, sluit u het notebook "
"zonder deze op te slaan."

#: notebook/static/notebook/js/notebook.js:3061
msgid "Notebook converted"
msgstr "Notebook geconverteerd"

#: notebook/static/notebook/js/notebook.js:3083
msgid "(No name)"
msgstr "(Geen naam)"

#: notebook/static/notebook/js/notebook.js:3131
#, python-format
msgid ""
"An unknown error occurred while loading this notebook. This version can load"
" notebook formats %s or earlier. See the server log for details."
msgstr ""
"Er is een onbekende fout opgetreden tijdens het laden van dit notebook. "
"Deze versie kan notebook-indelingen %s of eerder laden. Zie het "
"serverlogboek voor meer informatie."

#: notebook/static/notebook/js/notebook.js:3142
msgid "Error loading notebook"
msgstr "Fout bij het laden van notebook"

#: notebook/static/notebook/js/notebook.js:3243
msgid "Are you sure you want to revert the notebook to the latest checkpoint?"
msgstr ""
"Weet je zeker dat je het notebook wilt terugdraaien naar het laatste "
"checkpoint?"

#: notebook/static/notebook/js/notebook.js:3246
msgid "This cannot be undone."
msgstr "Dit kan niet ongedaan worden gemaakt."

#: notebook/static/notebook/js/notebook.js:3249
msgid "The checkpoint was last updated at:"
msgstr "Het checkpoint is voor het laatst bijgewerkt op:"

#: notebook/static/notebook/js/notebook.js:3260
msgid "Revert notebook to checkpoint"
msgstr "Notebook terugzetten naar checkpoint"

#: notebook/static/notebook/js/notificationarea.js:77
#: notebook/static/notebook/js/tour.js:61
#: notebook/static/notebook/js/tour.js:67
msgid "Edit Mode"
msgstr "Bewerkingsmodus"

#: notebook/static/notebook/js/notificationarea.js:84
#: notebook/static/notebook/js/notificationarea.js:88
#: notebook/static/notebook/js/tour.js:54
msgid "Command Mode"
msgstr "Opdrachtmodus"

#: notebook/static/notebook/js/notificationarea.js:95
msgid "Kernel Created"
msgstr "Kernel aangemaakt"

#: notebook/static/notebook/js/notificationarea.js:99
msgid "Connecting to kernel"
msgstr "Verbinding maken met kernel"

#: notebook/static/notebook/js/notificationarea.js:103
msgid "Not Connected"
msgstr "Niet verbonden"

#: notebook/static/notebook/js/notificationarea.js:106
msgid "click to reconnect"
msgstr "klik om opnieuw verbinding te maken"

#: notebook/static/notebook/js/notificationarea.js:115
msgid "Restarting kernel"
msgstr "Kernel herstarten"

#: notebook/static/notebook/js/notificationarea.js:129
msgid "Kernel Restarting"
msgstr "Kernel herstarten"

#: notebook/static/notebook/js/notificationarea.js:130
msgid "The kernel appears to have died. It will restart automatically."
msgstr ""
"De kernel lijkt te zijn gestopt. Hij wordt automatisch herstart."

#: notebook/static/notebook/js/notificationarea.js:140
#: notebook/static/notebook/js/notificationarea.js:198
#: notebook/static/notebook/js/notificationarea.js:218
msgid "Dead kernel"
msgstr "Gestopte kernel"

#: notebook/static/notebook/js/notificationarea.js:141
#: notebook/static/notebook/js/notificationarea.js:219
#: notebook/static/notebook/js/notificationarea.js:266
msgid "Kernel Dead"
msgstr "Kernel is gestopt"

#: notebook/static/notebook/js/notificationarea.js:145
msgid "Interrupting kernel"
msgstr "Kernel onderbreken"

#: notebook/static/notebook/js/notificationarea.js:151
msgid "No Connection to Kernel"
msgstr "Geen verbinding met kernel"

#: notebook/static/notebook/js/notificationarea.js:161
msgid ""
"A connection to the notebook server could not be established. The notebook "
"will continue trying to reconnect. Check your network connection or notebook"
" server configuration."
msgstr ""
"Er kan geen verbinding met de notebookserver worden gemaakt. Het notebook"
" blijft proberen opnieuw verbinding te maken. Controleer de configuratie van"
" uw netwerkverbinding of laptopserver."

#: notebook/static/notebook/js/notificationarea.js:166
msgid "Connection failed"
msgstr "Verbinding is mislukt"

#: notebook/static/notebook/js/notificationarea.js:179
msgid "No kernel"
msgstr "Geen kernel"

#: notebook/static/notebook/js/notificationarea.js:180
msgid "Kernel is not running"
msgstr "Kernel is niet actief"

#: notebook/static/notebook/js/notificationarea.js:187
msgid "Don't Restart"
msgstr "Niet herstarten"

#: notebook/static/notebook/js/notificationarea.js:187
msgid "Try Restarting Now"
msgstr "Probeer nu te herstarten"

#: notebook/static/notebook/js/notificationarea.js:191
msgid ""
"The kernel has died, and the automatic restart has failed. It is possible "
"the kernel cannot be restarted. If you are not able to restart the kernel, "
"you will still be able to save the notebook, but running code will no longer"
" work until the notebook is reopened."
msgstr ""
"De kernel is gestopt en de automatische herstart is mislukt. Het is "
"mogelijk dat de kernel niet kan worden herstart. Als u de kernel niet"
" start, kan u het notebook nog steeds opslaan, maar werkt de code "
"niet meer totdat het notebook opnieuw is geopend."

#: notebook/static/notebook/js/notificationarea.js:225
msgid "No Kernel"
msgstr "Geen kernel"

#: notebook/static/notebook/js/notificationarea.js:252
msgid "Failed to start the kernel"
msgstr "Kan de kernel niet starten"

#: notebook/static/notebook/js/notificationarea.js:272
#: notebook/static/notebook/js/notificationarea.js:292
#: notebook/static/notebook/js/notificationarea.js:306
msgid "Kernel Busy"
msgstr "Kernel bezet"

#: notebook/static/notebook/js/notificationarea.js:273
msgid "Kernel starting, please wait..."
msgstr "Kernel beginnen, wachten..."

#: notebook/static/notebook/js/notificationarea.js:279
#: notebook/static/notebook/js/notificationarea.js:286
msgid "Kernel Idle"
msgstr "Kernel Idle"

#: notebook/static/notebook/js/notificationarea.js:280
msgid "Kernel ready"
msgstr "Kernel klaar"

#: notebook/static/notebook/js/notificationarea.js:297
msgid "Using kernel: "
msgstr "Kernel gebruiken: "

#: notebook/static/notebook/js/notificationarea.js:298
msgid "Only candidate for language: %1$s was %2$s."
msgstr "Enige kandidaat voor taal: %1$s was %2$s."

#: notebook/static/notebook/js/notificationarea.js:319
msgid "Loading notebook"
msgstr "Notebook laden"

#: notebook/static/notebook/js/notificationarea.js:322
msgid "Notebook loaded"
msgstr "Notebook geladen"

#: notebook/static/notebook/js/notificationarea.js:325
msgid "Saving notebook"
msgstr "Notebook opslaan"

#: notebook/static/notebook/js/notificationarea.js:328
msgid "Notebook saved"
msgstr "Notebook opgeslagen"

#: notebook/static/notebook/js/notificationarea.js:331
msgid "Notebook save failed"
msgstr "Notebook opslaan is mislukt"

#: notebook/static/notebook/js/notificationarea.js:334
msgid "Notebook copy failed"
msgstr "Notebook kopiëren is mislukt"

#: notebook/static/notebook/js/notificationarea.js:339
msgid "Checkpoint created"
msgstr "Checkpoint gemaakt"

#: notebook/static/notebook/js/notificationarea.js:347
msgid "Checkpoint failed"
msgstr "Checkpoint is mislukt"

#: notebook/static/notebook/js/notificationarea.js:350
msgid "Checkpoint deleted"
msgstr "Checkpoint verwijderd"

#: notebook/static/notebook/js/notificationarea.js:353
msgid "Checkpoint delete failed"
msgstr "Checkpoint verwijderen is mislukt"

#: notebook/static/notebook/js/notificationarea.js:356
msgid "Restoring to checkpoint..."
msgstr "Herstellen naar checkpoint..."

#: notebook/static/notebook/js/notificationarea.js:359
msgid "Checkpoint restore failed"
msgstr "Checkpoint herstel is mislukt"

#: notebook/static/notebook/js/notificationarea.js:364
msgid "Autosave disabled"
msgstr "Automatisch opslaan uitgeschakeld"

#: notebook/static/notebook/js/notificationarea.js:367
#, python-format
msgid "Saving every %d sec."
msgstr "Het notebook wordt elke %d sec opgeslagen."

#: notebook/static/notebook/js/notificationarea.js:383
msgid "Trusted"
msgstr "Vertrouwd"

#: notebook/static/notebook/js/notificationarea.js:385
msgid "Not Trusted"
msgstr "Niet vertrouwd"

#: notebook/static/notebook/js/outputarea.js:75
msgid "click to expand output"
msgstr "klik om de uitvoer volledig weer te geven"

#: notebook/static/notebook/js/outputarea.js:79
msgid "click to expand output; double click to hide output"
msgstr ""
"klik om de uitvoer te laten verschijnen; dubbelklikken om uitvoer te verbergen"

#: notebook/static/notebook/js/outputarea.js:167
msgid "click to unscroll output; double click to hide"
msgstr "klik om de uitvoer te ontscrollen; dubbelklikken om te verbergen"

#: notebook/static/notebook/js/outputarea.js:174
msgid "click to scroll output; double click to hide"
msgstr "klik om de uitvoer te scrollen; dubbelklikken om te verbergen"

#: notebook/static/notebook/js/outputarea.js:422
msgid "Javascript error adding output!"
msgstr "Javascript fout bij het toevoegen van uitvoer!"

#: notebook/static/notebook/js/outputarea.js:427
msgid "See your browser Javascript console for more details."
msgstr "Zie uw browser Javascript console voor meer details."

#: notebook/static/notebook/js/outputarea.js:468
#, python-format
msgid "Out[%d]:"
msgstr "Out[%d]:"

#: notebook/static/notebook/js/outputarea.js:577
#, python-format
msgid "Unrecognized output: %s"
msgstr "Niet-herkende uitvoer: %s"

#: notebook/static/notebook/js/pager.js:36
msgid "Open the pager in an external window"
msgstr "De pager openen in een extern venster"

#: notebook/static/notebook/js/pager.js:45
msgid "Close the pager"
msgstr "De pager sluiten"

#: notebook/static/notebook/js/pager.js:148
msgid "Jupyter Pager"
msgstr "Jupyter Pager"

#: notebook/static/notebook/js/quickhelp.js:39
#: notebook/static/notebook/js/quickhelp.js:49
#: notebook/static/notebook/js/quickhelp.js:50
msgid "go to cell start"
msgstr "ga naar celstart"

#: notebook/static/notebook/js/quickhelp.js:40
#: notebook/static/notebook/js/quickhelp.js:51
#: notebook/static/notebook/js/quickhelp.js:52
msgid "go to cell end"
msgstr "ga naar het celeinde"

#: notebook/static/notebook/js/quickhelp.js:41
#: notebook/static/notebook/js/quickhelp.js:53
msgid "go one word left"
msgstr "één woord naar links gaan"

#: notebook/static/notebook/js/quickhelp.js:42
#: notebook/static/notebook/js/quickhelp.js:54
msgid "go one word right"
msgstr "ga een woord naar rechts"

#: notebook/static/notebook/js/quickhelp.js:43
#: notebook/static/notebook/js/quickhelp.js:55
msgid "delete word before"
msgstr "woord ervoor verwijderen"

#: notebook/static/notebook/js/quickhelp.js:44
#: notebook/static/notebook/js/quickhelp.js:56
msgid "delete word after"
msgstr "woord erna verwijderen"

#: notebook/static/notebook/js/quickhelp.js:61
msgid "code completion or indent"
msgstr "code aanvulling of inspringing"

#: notebook/static/notebook/js/quickhelp.js:62
msgid "tooltip"
msgstr "Tooltip"

#: notebook/static/notebook/js/quickhelp.js:63
msgid "indent"
msgstr "inspringing"

#: notebook/static/notebook/js/quickhelp.js:64
msgid "dedent"
msgstr "inspringing ongedaan maken"

#: notebook/static/notebook/js/quickhelp.js:65
msgid "select all"
msgstr "selecteer alle"

#: notebook/static/notebook/js/quickhelp.js:66
msgid "undo"
msgstr "Ongedaan maken"

#: notebook/static/notebook/js/quickhelp.js:67
#: notebook/static/notebook/js/quickhelp.js:68
msgid "redo"
msgstr "Opnieuw"

#: notebook/static/notebook/js/quickhelp.js:102
#: notebook/static/notebook/js/quickhelp.js:243
msgid "Shift"
msgstr "Shift"

#: notebook/static/notebook/js/quickhelp.js:103
msgid "Alt"
msgstr "Alt"

#: notebook/static/notebook/js/quickhelp.js:104
msgid "Up"
msgstr "Omhoog"

#: notebook/static/notebook/js/quickhelp.js:105
msgid "Down"
msgstr "Omlaag"

#: notebook/static/notebook/js/quickhelp.js:106
msgid "Left"
msgstr "Links"

#: notebook/static/notebook/js/quickhelp.js:107
msgid "Right"
msgstr "Recht"

#: notebook/static/notebook/js/quickhelp.js:108
#: notebook/static/notebook/js/quickhelp.js:246
msgid "Tab"
msgstr "Tab"

#: notebook/static/notebook/js/quickhelp.js:109
msgid "Caps Lock"
msgstr "CAPS-LOCK"

#: notebook/static/notebook/js/quickhelp.js:110
#: notebook/static/notebook/js/quickhelp.js:269
msgid "Esc"
msgstr "Esc"

#: notebook/static/notebook/js/quickhelp.js:111
msgid "Ctrl"
msgstr "Ctrl"

#: notebook/static/notebook/js/quickhelp.js:112
#: notebook/static/notebook/js/quickhelp.js:290
msgid "Enter"
msgstr "Enter"

#: notebook/static/notebook/js/quickhelp.js:113
msgid "Page Up"
msgstr "Pagina omhoog"

#: notebook/static/notebook/js/quickhelp.js:114
#: notebook/static/notebook/js/quickhelp.js:130
msgid "Page Down"
msgstr "Pagina omlaag"

#: notebook/static/notebook/js/quickhelp.js:115
msgid "Home"
msgstr "Home"

#: notebook/static/notebook/js/quickhelp.js:116
msgid "End"
msgstr "Einde"

#: notebook/static/notebook/js/quickhelp.js:117
#: notebook/static/notebook/js/quickhelp.js:245
msgid "Space"
msgstr "Spatie"

#: notebook/static/notebook/js/quickhelp.js:118
msgid "Backspace"
msgstr "Backspace"

#: notebook/static/notebook/js/quickhelp.js:119
msgid "Minus"
msgstr "Minus"

#: notebook/static/notebook/js/quickhelp.js:130
msgid "PageUp"
msgstr "PageUp"

#: notebook/static/notebook/js/quickhelp.js:197
msgid "The Jupyter Notebook has two different keyboard input modes."
msgstr "De Jupyter Notebook heeft twee verschillende toetsenbordinvoermodi."

#: notebook/static/notebook/js/quickhelp.js:199
msgid ""
"<b>Edit mode</b> allows you to type code or text into a cell and is "
"indicated by a green cell border."
msgstr ""
"<b>Edit-modus</b> u code of tekst typen in een cel en wordt aangegeven door "
"een groene celrand."

#: notebook/static/notebook/js/quickhelp.js:201
msgid ""
"<b>Command mode</b> binds the keyboard to notebook level commands and is "
"indicated by a grey cell border with a blue left margin."
msgstr ""
"<b>Command-modus</b> het toetsenbord bindt aan opdrachten op notebookniveau "
"en wordt aangegeven door een grijze celrand met een blauwe linkermarge."

#: notebook/static/notebook/js/quickhelp.js:222
#: notebook/static/notebook/js/tooltip.js:58
#: notebook/static/notebook/js/tooltip.js:69
msgid "Close"
msgstr "Sluiten"

#: notebook/static/notebook/js/quickhelp.js:225
msgid "Keyboard shortcuts"
msgstr "Sneltoetsen"

#: notebook/static/notebook/js/quickhelp.js:240
msgid "Command"
msgstr "Opdracht"

#: notebook/static/notebook/js/quickhelp.js:241
msgid "Control"
msgstr "Controle"

#: notebook/static/notebook/js/quickhelp.js:242
msgid "Option"
msgstr "Optie"

#: notebook/static/notebook/js/quickhelp.js:244
msgid "Return"
msgstr "Terug"

#: notebook/static/notebook/js/quickhelp.js:270
#, python-format
msgid "Command Mode (press %s to enable)"
msgstr "Opdrachtmodus (druk op %s om in te schakelen)"

#: notebook/static/notebook/js/quickhelp.js:272
msgid "Edit Shortcuts"
msgstr "Sneltoetsen bewerken"

#: notebook/static/notebook/js/quickhelp.js:275
msgid "edit command-mode keyboard shortcuts"
msgstr "Sneltoetsen in de opdrachtmodus bewerken"

#: notebook/static/notebook/js/quickhelp.js:292
#, python-format
msgid "Edit Mode (press %s to enable)"
msgstr "Bewerkingsmodus (druk op %s om in te schakelen)"

#: notebook/static/notebook/js/savewidget.js:49
msgid "Autosave Failed!"
msgstr "Automatisch opslaan is mislukt!"

#: notebook/static/notebook/js/savewidget.js:71
#: notebook/static/tree/js/notebooklist.js:846
#: notebook/static/tree/js/notebooklist.js:859
msgid "Rename"
msgstr "Hernoemen"

#: notebook/static/notebook/js/savewidget.js:78
#: notebook/static/tree/js/notebooklist.js:837
msgid "Enter a new notebook name:"
msgstr "Voer een nieuwe notebooknaam in:"

#: notebook/static/notebook/js/savewidget.js:86
msgid "Rename Notebook"
msgstr "De naam van notebook wijzigen"

#: notebook/static/notebook/js/savewidget.js:98
msgid ""
"Invalid notebook name. Notebook names must have 1 or more characters and can"
" contain any characters except :/\\. Please enter a new notebook name:"
msgstr ""
"Ongeldige notebooknaam. Notebook-namen moeten 1 of meer tekens bevatten"
" en kunnen tekens bevatten, behalve :/\\. Voer een nieuwe notebooknaam "
"in:"

#: notebook/static/notebook/js/savewidget.js:103
msgid "Renaming..."
msgstr "Hernoemen..."

#: notebook/static/notebook/js/savewidget.js:109
msgid "Unknown error"
msgstr "Onbekende fout"

#: notebook/static/notebook/js/savewidget.js:178
msgid "no checkpoint"
msgstr "geen checkpoint"

#: notebook/static/notebook/js/savewidget.js:193
#, python-format
msgid "Last Checkpoint: %s"
msgstr "Laatste checkpoint: %s"

#: notebook/static/notebook/js/savewidget.js:217
msgid "(unsaved changes)"
msgstr "(niet-opgeslagen wijzigingen)"

#: notebook/static/notebook/js/savewidget.js:219
msgid "(autosaved)"
msgstr "(automatisch opgeslagen)"

#: notebook/static/notebook/js/searchandreplace.js:74
#, python-format
msgid ""
"Warning: too many matches (%d). Some changes might not be shown or applied."
msgstr ""
"Waarschuwing: te veel matches (%d). Sommige wijzigingen worden mogelijk "
"niet weergegeven of toegepast."

#: notebook/static/notebook/js/searchandreplace.js:77
#, python-format
msgid "%d match"
msgid_plural "%d matches"
msgstr[0] "%d match"
msgstr[1] "%d matches"

#: notebook/static/notebook/js/searchandreplace.js:145
msgid "More than 100 matches, aborting"
msgstr "Meer dan 100 matches, afbreken"

#: notebook/static/notebook/js/searchandreplace.js:166
msgid "Use regex (JavaScript regex syntax)"
msgstr "Regex gebruiken (JavaScript regex syntax)"

#: notebook/static/notebook/js/searchandreplace.js:174
msgid "Replace in selected cells"
msgstr "Vervangen in geselecteerde cellen"

#: notebook/static/notebook/js/searchandreplace.js:181
msgid "Match case"
msgstr "Hoofdletters matchen"

#: notebook/static/notebook/js/searchandreplace.js:187
msgid "Find"
msgstr "Zoek"

#: notebook/static/notebook/js/searchandreplace.js:203
msgid "Replace"
msgstr "Vervangen"

#: notebook/static/notebook/js/searchandreplace.js:255
msgid "No matches, invalid or empty regular expression"
msgstr "Geen overeenkomsten, ongeldige of lege reguliere expressie"

#: notebook/static/notebook/js/searchandreplace.js:370
msgid "Replace All"
msgstr "Alles vervangen"

#: notebook/static/notebook/js/searchandreplace.js:374
msgid "Find and Replace"
msgstr "Zoeken en vervangen"

#: notebook/static/notebook/js/searchandreplace.js:400
#: notebook/static/notebook/js/searchandreplace.js:401
msgid "find and replace"
msgstr "zoeken en vervangen"

#: notebook/static/notebook/js/textcell.js:551
msgid ""
"Write raw LaTeX or other formats here, for use with nbconvert. It will not "
"be rendered in the notebook. When passing through nbconvert, a Raw Cell's "
"content is added to the output unmodified."
msgstr ""
"Schrijf hier in LaTeX of ander format, voor gebruik met nbconvert. Het "
"wordt niet weergegeven in het notebook. Bij het doorlopen van nbconvert "
"wordt de inhoud van een Raw Cell ongewijzigd aan de uitvoer toegevoegd."

#: notebook/static/notebook/js/tooltip.js:41
msgid "Grow the tooltip vertically (press shift-tab twice)"
msgstr "De tooltip verticaal laten groeien (druk twee keer op shift-tab)"

#: notebook/static/notebook/js/tooltip.js:48
msgid "show the current docstring in pager (press shift-tab 4 times)"
msgstr "de huidige docstring weergeven in pager (druk 4 keer op shift-tab)"

#: notebook/static/notebook/js/tooltip.js:49
msgid "Open in Pager"
msgstr "Openen in Pager"

#: notebook/static/notebook/js/tooltip.js:68
msgid "Tooltip will linger for 10 seconds while you type"
msgstr "De tooltip blijft 10 seconden hangen terwijl u typt"

#: notebook/static/notebook/js/tour.js:27
msgid "Welcome to the Notebook Tour"
msgstr "Welkom bij de Notebook Rondleiding"

#: notebook/static/notebook/js/tour.js:30
msgid ""
"You can use the left and right arrow keys to go backwards and forwards."
msgstr "U kan de pijltjestoetsen links en rechts gebruiken om heen en weer te gaan."

#: notebook/static/notebook/js/tour.js:33
msgid "Filename"
msgstr "Bestandsnaam"

#: notebook/static/notebook/js/tour.js:35
msgid "Click here to change the filename for this notebook."
msgstr "Klik hier om de bestandsnaam voor dit notebook te wijzigen."

#: notebook/static/notebook/js/tour.js:39
msgid "Notebook Menubar"
msgstr "Menubalk notebook"

#: notebook/static/notebook/js/tour.js:40
msgid ""
"The menubar has menus for actions on the notebook, its cells, and the kernel"
" it communicates with."
msgstr ""
"De menubalk heeft menu's voor acties op het notebook, de cellen en de "
"kernel waarmee het communiceert."

#: notebook/static/notebook/js/tour.js:44
msgid "Notebook Toolbar"
msgstr "Werkbalk Notebook"

#: notebook/static/notebook/js/tour.js:45
msgid ""
"The toolbar has buttons for the most common actions. Hover your mouse over "
"each button for more information."
msgstr ""
"De werkbalk heeft knoppen voor de meest voorkomende acties. Beweeg met je "
"muis over elke knop voor meer informatie."

#: notebook/static/notebook/js/tour.js:48
msgid "Mode Indicator"
msgstr "Modus-indicator"

#: notebook/static/notebook/js/tour.js:50
msgid ""
"The Notebook has two modes: Edit Mode and Command Mode. In this area, an "
"indicator can appear to tell you which mode you are in."
msgstr ""
"De notebook heeft twee modi: De modus bewerken en de opdrachtmodus. In dit "
"gebied kan een indicator verschijnen om u te vertellen in welke modus u zich"
" bevindt."

#: notebook/static/notebook/js/tour.js:58
msgid ""
"Right now you are in Command Mode, and many keyboard shortcuts are "
"available. In this mode, no icon is displayed in the indicator area."
msgstr ""
"Op dit moment bent u in de opdrachtmodus en zijn er veel sneltoetsen "
"beschikbaar. In deze modus wordt geen pictogram weergegeven in het "
"indicatorgebied."

#: notebook/static/notebook/js/tour.js:64
msgid ""
"Pressing <code>Enter</code> or clicking in the input text area of the cell "
"switches to Edit Mode."
msgstr ""
"Als u op <code>Enter </code> drukt of klikt in het invoertekstgebied van de cel, "
"schakelt u over naar de bewerkingsmodus."

#: notebook/static/notebook/js/tour.js:70
msgid ""
"Notice that the border around the currently active cell changed color. "
"Typing will insert text into the currently active cell."
msgstr ""
"De rand rond de actieve cel is van kleur veranderd. Als u typt, wordt tekst "
"ingevoegd in de actieve cel."

#: notebook/static/notebook/js/tour.js:73
msgid "Back to Command Mode"
msgstr "Terug naar de opdrachtmodus"

#: notebook/static/notebook/js/tour.js:76
msgid ""
"Pressing <code>Esc</code> or clicking outside of the input text area takes "
"you back to Command Mode."
msgstr ""
"Als u op <code>Esc drukt</code> of buiten het invoertekstgebied klikt, gaat "
"u terug naar de opdrachtmodus."

#: notebook/static/notebook/js/tour.js:79
msgid "Keyboard Shortcuts"
msgstr "Sneltoetsen"

#: notebook/static/notebook/js/tour.js:91
msgid "You can click here to get a list of all of the keyboard shortcuts."
msgstr "U hier klikken om een lijst van alle sneltoetsen te krijgen."

#: notebook/static/notebook/js/tour.js:94
#: notebook/static/notebook/js/tour.js:100
msgid "Kernel Indicator"
msgstr "Kernel Indicator"

#: notebook/static/notebook/js/tour.js:97
msgid ""
"This is the Kernel indicator. It looks like this when the Kernel is idle."
msgstr ""
"Dit is de Kernel indicator. Het ziet er zo uit als de Kernel niet actief is."

#: notebook/static/notebook/js/tour.js:103
msgid "The Kernel indicator looks like this when the Kernel is busy."
msgstr "De Kernel indicator ziet er als volgt uit wanneer de Kernel bezig is."

#: notebook/static/notebook/js/tour.js:107
msgid "Interrupting the Kernel"
msgstr "De kernel onderbreken"

#: notebook/static/notebook/js/tour.js:109
msgid "To cancel a computation in progress, you can click here."
msgstr "Als u een actieve berekening wilt annuleren, kan u hier klikken."

#: notebook/static/notebook/js/tour.js:114
msgid "Notification Area"
msgstr "Systeemvak"

#: notebook/static/notebook/js/tour.js:115
msgid ""
"Messages in response to user actions (Save, Interrupt, etc.) appear here."
msgstr ""
"Berichten in reactie op acties van gebruikers (Opslaan, Onderbreken, enz.) "
"worden hier weergegeven."

#: notebook/static/notebook/js/tour.js:117
msgid "End of Tour"
msgstr "Einde van de Tour"

#: notebook/static/notebook/js/tour.js:120
msgid "This concludes the Jupyter Notebook User Interface Tour."
msgstr "Dit concludeert de Jupyter Notebook User Interface Rondleiding."

#: notebook/static/notebook/js/celltoolbarpresets/attachments.js:32
msgid "Edit Attachments"
msgstr "Bijlagen bewerken"

#: notebook/static/notebook/js/celltoolbarpresets/default.js:19
msgid "Cell"
msgstr "Cel"

#: notebook/static/notebook/js/celltoolbarpresets/default.js:29
#: notebook/static/notebook/js/celltoolbarpresets/default.js:47
msgid "Edit Metadata"
msgstr "Metagegevens bewerken"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:22
msgid "Custom"
msgstr "Aangepaste"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:32
msgid "Set the MIME type of the raw cell:"
msgstr "Stel het MIME-type van de raw cel in:"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:40
msgid "Raw Cell MIME Type"
msgstr "Raw Cell MIME-type"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:74
msgid "Raw NBConvert Format"
msgstr "Raw NBConvert-indeling"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:81
msgid "Raw Cell Format"
msgstr "Raw-celnotatie"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:15
msgid "Slide"
msgstr "Dia"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:16
msgid "Sub-Slide"
msgstr "Subdia"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:17
msgid "Fragment"
msgstr "Fragment"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:18
msgid "Skip"
msgstr "Overslaan"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:19
msgid "Notes"
msgstr "Notities"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:35
msgid "Slide Type"
msgstr "Diatype"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:41
msgid "Slideshow"
msgstr "Diavoorstelling"

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:133
msgid "Add tag"
msgstr "Tag toevoegen"

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:163
msgid ""
"Edit the list of tags below. All whitespace is treated as tag separators."
msgstr ""
"Bewerk de onderstaande lijst met tags. Alle witruimte wordt behandeld als "
"tagscheidingstekens."

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:172
msgid "Edit the tags"
msgstr "De tags bewerken"

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:186
msgid "Edit Tags"
msgstr "Tags bewerken"

#: notebook/static/tree/js/kernellist.js:86
#: notebook/static/tree/js/terminallist.js:105
msgid "Shutdown"
msgstr "Afsluiten"

#: notebook/static/tree/js/newnotebook.js:70
#, python-format
msgid "Create a new notebook with %s"
msgstr "Een nieuw notebook maken met %s"

#: notebook/static/tree/js/newnotebook.js:101
msgid "An error occurred while creating a new notebook."
msgstr ""
"Er is een fout opgetreden tijdens het maken van een nieuw notebook."

#: notebook/static/tree/js/notebooklist.js:122
msgid "Creating File Failed"
msgstr "Bestand maken is mislukt"

#: notebook/static/tree/js/notebooklist.js:124
msgid "An error occurred while creating a new file."
msgstr "Er is een fout opgetreden tijdens het maken van een nieuw bestand."

#: notebook/static/tree/js/notebooklist.js:142
msgid "Creating Folder Failed"
msgstr "Map maken is mislukt"

#: notebook/static/tree/js/notebooklist.js:144
msgid "An error occurred while creating a new folder."
msgstr "Er is een fout opgetreden tijdens het maken van een nieuwe map."

#: notebook/static/tree/js/notebooklist.js:271
msgid "Failed to read file"
msgstr "Bestand niet kunnen worden gelezen"

#: notebook/static/tree/js/notebooklist.js:272
#, python-format
msgid "Failed to read file %s"
msgstr "Bestand %s niet kunnen lezen"

#: notebook/static/tree/js/notebooklist.js:283
#, python-format
msgid "The file size is %d MB. Do you still want to upload it?"
msgstr "De bestandsgrootte is %d MB. Wil je het nog steeds uploaden?"

#: notebook/static/tree/js/notebooklist.js:286
msgid "Large file size warning"
msgstr "Waarschuwing voor grote bestandsgrootte"

#: notebook/static/tree/js/notebooklist.js:355
msgid "Server error: "
msgstr "Serverfout: "

#: notebook/static/tree/js/notebooklist.js:390
msgid "The notebook list is empty."
msgstr "De lijst met notebooks is leeg."

#: notebook/static/tree/js/notebooklist.js:463
msgid "Click here to rename, delete, etc."
msgstr "Klik hier om de naam te wijzigen, te verwijderen, enz."

#: notebook/static/tree/js/notebooklist.js:503
msgid "Running"
msgstr "Actieve Processen"

#: notebook/static/tree/js/notebooklist.js:835
msgid "Enter a new file name:"
msgstr "Voer een nieuwe bestandsnaam in:"

#: notebook/static/tree/js/notebooklist.js:836
msgid "Enter a new directory name:"
msgstr "Voer een nieuwe mapnaam in:"

#: notebook/static/tree/js/notebooklist.js:838
msgid "Enter a new name:"
msgstr "Voer een nieuwe naam in:"

#: notebook/static/tree/js/notebooklist.js:843
msgid "Rename file"
msgstr "Naamvan het bestand wijzigen"

#: notebook/static/tree/js/notebooklist.js:844
msgid "Rename directory"
msgstr "Naamvan naam wijzigen"

#: notebook/static/tree/js/notebooklist.js:845
msgid "Rename notebook"
msgstr "De naam van notebook wijzigen"

#: notebook/static/tree/js/notebooklist.js:859
msgid "Move"
msgstr "Verplaatsen"

#: notebook/static/tree/js/notebooklist.js:875
msgid "An error occurred while renaming \"%1$s\" to \"%2$s\"."
msgstr "Er is een fout opgetreden bij het hernoemen van \"%1$s\" naar \"%2$s\"."

#: notebook/static/tree/js/notebooklist.js:878
msgid "Rename Failed"
msgstr "Naam wijzigen Van mislukt"

#: notebook/static/tree/js/notebooklist.js:927
#, python-format
msgid "Enter a new destination directory path for this item:"
msgid_plural "Enter a new destination directory path for these %d items:"
msgstr[0] "Voer een nieuw bestemmingsmap-pad in voor dit item:"
msgstr[1] "Voer een nieuw bestemmingsmap-pad in voor deze %d items:"

#: notebook/static/tree/js/notebooklist.js:940
#, python-format
msgid "Move an Item"
msgid_plural "Move %d Items"
msgstr[0] "Een item verplaatsen"
msgstr[1] "%d objecten verplaatsen"

#: notebook/static/tree/js/notebooklist.js:959
msgid "An error occurred while moving \"%1$s\" from \"%2$s\" to \"%3$s\"."
msgstr ""
"Er is een fout opgetreden tijdens het verplaatsen van \"%1$s\" van \"%2$s\" "
"naar \"%3$s\"."

#: notebook/static/tree/js/notebooklist.js:961
msgid "Move Failed"
msgstr "Verplaatsen mislukt"

#: notebook/static/tree/js/notebooklist.js:1007
#, python-format
msgid "Are you sure you want to permanently delete: \"%s\"?"
msgid_plural ""
"Are you sure you want to permanently delete the %d files or folders "
"selected?"
msgstr[0] "Weet u zeker dat u het volgende definitief wilt verwijderen: \"%s\"?"
msgstr[1] ""
"Weet u zeker dat u de geselecteerde %d bestanden of mappen permanent wilt "
"verwijderen?"

#: notebook/static/tree/js/notebooklist.js:1035
#, python-format
msgid "An error occurred while deleting \"%s\"."
msgstr "Er is een fout opgetreden tijdens het verwijderen van \"%s\"."

#: notebook/static/tree/js/notebooklist.js:1037
msgid "Delete Failed"
msgstr "Verwijderen mislukt"

#: notebook/static/tree/js/notebooklist.js:1078
#, python-format
msgid "Are you sure you want to duplicate: \"%s\"?"
msgid_plural "Are you sure you want to duplicate the %d files selected?"
msgstr[0] "Weet u zeker dat u wilt dupliceren: \"%s\"?"
msgstr[1] "Weet u zeker dat u de geselecteerde %d bestanden wilt dupliceren?"

#: notebook/static/tree/js/notebooklist.js:1088
msgid "Duplicate"
msgstr "Dupliceren"

#: notebook/static/tree/js/notebooklist.js:1102
#, python-format
msgid "An error occurred while duplicating \"%s\"."
msgstr "Er is een fout opgetreden tijdens het dupliceren van \"%s\"."

#: notebook/static/tree/js/notebooklist.js:1104
msgid "Duplicate Failed"
msgstr "Dupliceren is mislukt"

#: notebook/static/tree/js/notebooklist.js:1323
msgid "Upload"
msgstr "Uploaden"

#: notebook/static/tree/js/notebooklist.js:1332
msgid "Invalid file name"
msgstr "Ongeldige bestandsnaam"

#: notebook/static/tree/js/notebooklist.js:1333
msgid "File names must be at least one character and not start with a period"
msgstr ""
"Bestandsnamen moeten ten minste één teken zijn en niet beginnen met een "
"periode"

#: notebook/static/tree/js/notebooklist.js:1362
msgid "Cannot upload invalid Notebook"
msgstr "Kan ongeldig notebook niet uploaden"

#: notebook/static/tree/js/notebooklist.js:1395
#, python-format
msgid "There is already a file named \"%s\". Do you want to replace it?"
msgstr "Er is al een bestand met de naam \"%s\". Wilt u het vervangen?"

#: notebook/static/tree/js/notebooklist.js:1397
msgid "Replace file"
msgstr "Bestand vervangen"
