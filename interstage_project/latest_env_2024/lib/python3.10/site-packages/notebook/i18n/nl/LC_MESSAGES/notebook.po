# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-07-08 21:52-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

#: notebook/notebookapp.py:53
msgid "The Jupyter Notebook requires tornado >= 4.0"
msgstr "De Jupyter Notebook vereist tornado >= 4.0"

#: notebook/notebookapp.py:57
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have < 1.1.0"
msgstr "De Jupyter Notebook vereist tornado >= 4.0, maar je hebt < 1.1.0"

#: notebook/notebookapp.py:59
#, python-format
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have %s"
msgstr "De Jupyter Notebook vereist tornado >= 4.0, maar je hebt %s"

#: notebook/notebookapp.py:209
msgid "The `ignore_minified_js` flag is deprecated and no longer works."
msgstr "De vlag 'ignore_minified_js' is afgeschaft en werkt niet meer."

#: notebook/notebookapp.py:210
#, python-format
msgid ""
"Alternatively use `%s` when working on the notebook's Javascript and LESS"
msgstr ""
"U ook '%s' gebruiken bij het werken aan de Javascript van de notebook en "
"LESS"

#: notebook/notebookapp.py:211
msgid ""
"The `ignore_minified_js` flag is deprecated and will be removed in Notebook "
"6.0"
msgstr ""
"De vlag 'ignore_minified_js' wordt afgeschaft en wordt verwijderd in "
"Notebook 6.0"

#: notebook/notebookapp.py:389
msgid "List currently running notebook servers."
msgstr "Lijst met momenteel draaiende notebookservers."

#: notebook/notebookapp.py:393
msgid "Produce machine-readable JSON output."
msgstr "Productie computer-leesbare JSON-uitvoer."

#: notebook/notebookapp.py:397
msgid ""
"If True, each line of output will be a JSON object with the details from the"
" server info file."
msgstr ""
"Als dit True is, zal elke uitvoerregel een JSON-object worden met de details uit het "
"serverinfobestand."

#: notebook/notebookapp.py:402
msgid "Currently running servers:"
msgstr "Momenteel draaiende servers:"

#: notebook/notebookapp.py:419
msgid "Don't open the notebook in a browser after startup."
msgstr "Open het notebook niet in een browser na het opstarten."

#: notebook/notebookapp.py:423
msgid ""
"DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib."
msgstr ""
"UITGESCHAKELD: gebruik %pylab of %matplotlib in het notebook om "
"matplotlib in te schakelen."

#: notebook/notebookapp.py:439
msgid "Allow the notebook to be run from root user."
msgstr "Sta toe dat het notebook vanaf de root user kan worden uitgevoerd."

#: notebook/notebookapp.py:470
msgid ""
"The Jupyter HTML Notebook.\n"
"    \n"
"    This launches a Tornado based HTML Notebook Server that serves up an HTML5/Javascript Notebook client."
msgstr ""
"De Jupyter HTML Notebook.\n"
"    \n"
"Hiermee wordt een op Tornado gebaseerde HTML-notebookserver gelanceerd die een HTML5/Javascript-laptopclient bedient."

#: notebook/notebookapp.py:509
msgid ""
"Deprecated: Use minified JS file or not, mainly use during dev to avoid JS "
"recompilation"
msgstr ""
"Afgeschaft: Gebruik minified JS-bestand of niet, voornamelijk gebruiken "
"tijdens dev om JS recompilatie te voorkomen"

#: notebook/notebookapp.py:540
msgid "Set the Access-Control-Allow-Credentials: true header"
msgstr "De access-control-allow-credentials instellen: true header"

#: notebook/notebookapp.py:544
msgid "Whether to allow the user to run the notebook as root."
msgstr "Of de gebruiker het notebook als root mag activeren."

#: notebook/notebookapp.py:548
msgid "The default URL to redirect to from `/`"
msgstr "De standaard-URL om naar '/' te leiden"

#: notebook/notebookapp.py:552
msgid "The IP address the notebook server will listen on."
msgstr "Het IP-adres waar de notebookserver op geactiveerd wordt."

#: notebook/notebookapp.py:565
#, python-format
msgid ""
"Cannot bind to localhost, using 127.0.0.1 as default ip\n"
"%s"
msgstr ""
"Kan niet binden aan localhost, met 127.0.0.1 als standaardip\n"
"%s"

#: notebook/notebookapp.py:579
msgid "The port the notebook server will listen on."
msgstr "De port waarop de notebookserver geactiveerd wordt."

#: notebook/notebookapp.py:583
msgid ""
"The number of additional ports to try if the specified port is not "
"available."
msgstr ""
"Het aantal extra ports dat moet worden geprobeerd als de opgegeven port "
"niet beschikbaar is."

#: notebook/notebookapp.py:587
msgid "The full path to an SSL/TLS certificate file."
msgstr "Het volledige pad naar een SSL/TLS-certificaatbestand."

#: notebook/notebookapp.py:591
msgid "The full path to a private key file for usage with SSL/TLS."
msgstr ""
"Het volledige pad naar een privésleutelbestand voor gebruik met SSL/TLS."

#: notebook/notebookapp.py:595
msgid ""
"The full path to a certificate authority certificate for SSL/TLS client "
"authentication."
msgstr ""
"Het volledige pad naar een certificaat van certificaatautoriteit voor "
"SSL/TLS-clientverificatie."

#: notebook/notebookapp.py:599
msgid "The file where the cookie secret is stored."
msgstr "Het bestand waarin het cookiegeheim wordt opgeslagen."

#: notebook/notebookapp.py:628
#, python-format
msgid "Writing notebook server cookie secret to %s"
msgstr "Cookiegeheim voor notebookserver schrijven naar %s"

#: notebook/notebookapp.py:635
#, python-format
msgid "Could not set permissions on %s"
msgstr "Kan geen machtigingen instellen op %s"

#: notebook/notebookapp.py:640
msgid ""
"Token used for authenticating first-time connections to the server.\n"
"\n"
"        When no password is enabled,\n"
"        the default is to generate a new, random token.\n"
"\n"
"        Setting to an empty string disables authentication altogether, which is NOT RECOMMENDED.\n"
"        "
msgstr ""
"Token wordt gebruikt voor het verifiëren van eerste verbindingen met de server.\n"
"\n"
"Wanneer er geen wachtwoord is ingeschakeld,\n"
"        de standaardinstelling is het genereren van een nieuwe, willekeurige token.\n"
"\n"
"Als u een lege tekenreeks instelt, wordt de verificatie helemaal uitgeschakeld, wat niet wordt aanbevolen.\n"
"        "

#: notebook/notebookapp.py:650
msgid ""
"One-time token used for opening a browser.\n"
"        Once used, this token cannot be used again.\n"
"        "
msgstr ""
"Eenmalige token die wordt gebruikt voor het openen van een browser.\n"
"        Eenmaal gebruikt, kan dit token niet opnieuw worden gebruikt.\n"
"        "

#: notebook/notebookapp.py:726
msgid ""
"Specify Where to open the notebook on startup. This is the\n"
"        `new` argument passed to the standard library method `webbrowser.open`.\n"
"        The behaviour is not guaranteed, but depends on browser support. Valid\n"
"        values are:\n"
"            2 opens a new tab,\n"
"            1 opens a new window,\n"
"            0 opens in an existing window.\n"
"        See the `webbrowser.open` documentation for details.\n"
"        "
msgstr ""
"Geef op waar u het notebook moet openen bij het opstarten. Dit is de\n"
"        'nieuw' argument doorgegeven aan de standaard bibliotheek methode 'webbrowser.open'.\n"
"        Het gedrag is niet gegarandeerd, maar is afhankelijk van browserondersteuning. Geldig\n"
"        waarden zijn:\n"
"            2 opent een nieuw tabblad,\n"
"            1 opent een nieuw venster,\n"
"            0 wordt geopend in een bestaand venster.\n"
"        Zie de documentatie 'webbrowser.open' voor meer informatie.\n"
"        "

#: notebook/notebookapp.py:737
msgid "DEPRECATED, use tornado_settings"
msgstr "DEPRECATED, gebruik tornado_settings"

#: notebook/notebookapp.py:742
msgid ""
"\n"
"    webapp_settings is deprecated, use tornado_settings.\n"
msgstr ""
"\n"
"webapp_settings is deprecated, gebruik tornado_settings.\n"

#: notebook/notebookapp.py:746
msgid ""
"Supply overrides for the tornado.web.Application that the Jupyter notebook "
"uses."
msgstr ""
"Geef extra instellingen voor de tornado.web.Application die gebruikt wordt door de "
" Jupyter notebook."

#: notebook/notebookapp.py:750
msgid ""
"\n"
"        Set the tornado compression options for websocket connections.\n"
"\n"
"        This value will be returned from :meth:`WebSocketHandler.get_compression_options`.\n"
"        None (default) will disable compression.\n"
"        A dict (even an empty one) will enable compression.\n"
"\n"
"        See the tornado docs for WebSocketHandler.get_compression_options for details.\n"
"        "
msgstr ""
"\n"
"Stel de tornadocompressieopties in voor websocketverbindingen.\n"
"\n"
"Deze waarde wordt geretourneerd van :meth:'WebSocketHandler.get_compression_options'.\n"
"        Geen (standaard) schakelt compressie uit.\n"
"        Een dict (zelfs een lege) zal compressie mogelijk maken.\n"
"\n"
"Zie de tornadodocumenten voor WebSocketHandler.get_compression_options voor meer informatie.\n"
"        "

#: notebook/notebookapp.py:761
msgid "Supply overrides for terminado. Currently only supports \"shell_command\"."
msgstr ""
"Supply overrides voor terminado. Ondersteunt momenteel alleen een "
"\"shell_command\"."

#: notebook/notebookapp.py:764
msgid ""
"Extra keyword arguments to pass to `set_secure_cookie`. See tornado's "
"set_secure_cookie docs for details."
msgstr ""
"Extra trefwoordargumenten om door te geven aan 'set_secure_cookie'. Zie "
"tornado's set_secure_cookie documenten voor meer informatie."

#: notebook/notebookapp.py:768
msgid ""
"Supply SSL options for the tornado HTTPServer.\n"
"            See the tornado docs for details."
msgstr ""
"SSL-opties leveren voor de tornado HTTPServer.\n"
"            Zie de tornado docs voor meer informatie."

#: notebook/notebookapp.py:772
msgid "Supply extra arguments that will be passed to Jinja environment."
msgstr ""
"Vul extra argumenten aan die zullen worden doorgegeven aan de Jinja environment."

#: notebook/notebookapp.py:776
msgid "Extra variables to supply to jinja templates when rendering."
msgstr "Extra variabelen om aan te vullen aan de jinja-sjablonen bij het renderen."

#: notebook/notebookapp.py:812
msgid "DEPRECATED use base_url"
msgstr "DEPRECATED gebruik base_url"

#: notebook/notebookapp.py:816
msgid "base_project_url is deprecated, use base_url"
msgstr "base_project_url is deprecated, gebruik base_url"

#: notebook/notebookapp.py:832
msgid "Path to search for custom.js, css"
msgstr "Pad om te zoeken naar custom.js, css"

#: notebook/notebookapp.py:844
msgid ""
"Extra paths to search for serving jinja templates.\n"
"\n"
"        Can be used to override templates from notebook.templates."
msgstr ""
"Extra paden om te zoeken voor het activeren van jinja-sjablonen.\n"
"\n"
"Kan worden gebruikt om sjablonen van notebook.templates te overschrijven."

#: notebook/notebookapp.py:855
msgid "extra paths to look for Javascript notebook extensions"
msgstr "extra paden om te zoeken naar Javascript-notebookextensies"

#: notebook/notebookapp.py:900
#, python-format
msgid "Using MathJax: %s"
msgstr "MathJax gebruiken: %s"

#: notebook/notebookapp.py:903
msgid "The MathJax.js configuration file that is to be used."
msgstr "Het configuratiebestand MathJax.js dat moet worden gebruikt."

#: notebook/notebookapp.py:908
#, python-format
msgid "Using MathJax configuration file: %s"
msgstr "MathJax-configuratiebestand gebruiken: %s"

#: notebook/notebookapp.py:914
msgid "The notebook manager class to use."
msgstr "De notebook manager klasse te gebruiken."

#: notebook/notebookapp.py:920
msgid "The kernel manager class to use."
msgstr "De kernel manager klasse om te gebruiken."

#: notebook/notebookapp.py:926
msgid "The session manager class to use."
msgstr "De sessie manager klasse die u gebruiken."

#: notebook/notebookapp.py:932
msgid "The config manager class to use"
msgstr "De config manager klasse te gebruiken"

#: notebook/notebookapp.py:953
msgid "The login handler class to use."
msgstr "De login handler klasse te gebruiken."

#: notebook/notebookapp.py:960
msgid "The logout handler class to use."
msgstr "De afmeld handler klasse die u wilt gebruiken."

#: notebook/notebookapp.py:964
msgid ""
"Whether to trust or not X-Scheme/X-Forwarded-Proto and X-Real-"
"Ip/X-Forwarded-For headerssent by the upstream reverse proxy. Necessary if "
"the proxy handles SSL"
msgstr ""
"X-Scheme/X-Forwarded-Proto en X-Real-Ip/X-Forwarded-For headerssent door de "
"upstream reverse proxy al dan niet vertrouwen. Noodzakelijk als de proxy SSL"
" verwerkt"

#: notebook/notebookapp.py:976
msgid ""
"\n"
"        DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib.\n"
"        "
msgstr ""
"\n"
"UITGESCHAKELD: gebruik %pylab of %matplotlib in het notebook om matplotlib in te schakelen.\n"
"        "

#: notebook/notebookapp.py:988
msgid "Support for specifying --pylab on the command line has been removed."
msgstr ""
"Ondersteuning voor het opgeven van --pylab op de opdrachtregel is "
"verwijderd."

#: notebook/notebookapp.py:990
msgid "Please use `%pylab{0}` or `%matplotlib{0}` in the notebook itself."
msgstr "Gebruik '%pylab{0}' of '%matplotlib{0}' in het notebook zelf."

#: notebook/notebookapp.py:995
msgid "The directory to use for notebooks and kernels."
msgstr "De map die u wilt gebruiken voor notebooks en kernels."

#: notebook/notebookapp.py:1018
#, python-format
msgid "No such notebook dir: '%r'"
msgstr "Geen dergelijke notebook dir: '%r'"

#: notebook/notebookapp.py:1031
msgid "DEPRECATED use the nbserver_extensions dict instead"
msgstr "DEPRECATED gebruikt in plaats daarvan de nbserver_extensions dict"

#: notebook/notebookapp.py:1036
msgid "server_extensions is deprecated, use nbserver_extensions"
msgstr "server_extensions is afgeschaft, gebruik nbserver_extensions"

#: notebook/notebookapp.py:1040
msgid ""
"Dict of Python modules to load as notebook server extensions.Entry values "
"can be used to enable and disable the loading ofthe extensions. The "
"extensions will be loaded in alphabetical order."
msgstr ""
"Dict van Python-modules te laden als notebook server extensies. "
"Invoerwaarden kunnen worden gebruikt om het laden van de extensies in en uit"
" te schakelen. De extensies worden in alfabetische volgorde geladen."

#: notebook/notebookapp.py:1049
msgid "Reraise exceptions encountered loading server extensions?"
msgstr "Exceptions opnieuw weergeven die geraised waren tijdens het laden van"
" de server-extensies?"

#: notebook/notebookapp.py:1052
msgid ""
"(msgs/sec)\n"
"        Maximum rate at which messages can be sent on iopub before they are\n"
"        limited."
msgstr ""
"(msgs/sec)\n"
"        Maximale ratio waarmee berichten op iopub kunnen worden verzonden voordat ze\n"
"        worden beperkt."

#: notebook/notebookapp.py:1056
msgid ""
"(bytes/sec)\n"
"        Maximum rate at which stream output can be sent on iopub before they are\n"
"        limited."
msgstr ""
"(bytes/sec)\n"
"        Maximale ratio waarmee streamoutput op iopub kan worden verzonden voordat ze\n"
"        worden beperkt."

#: notebook/notebookapp.py:1060
msgid ""
"(sec) Time window used to \n"
"        check the message and data rate limits."
msgstr ""
"(sec) Tijdvenster gebruikt om \n"
"        de limieten voor het verzenden van berichten en de gegevenssnelheiden te"
" controleren."

#: notebook/notebookapp.py:1071
#, python-format
msgid "No such file or directory: %s"
msgstr "Geen dergelijk bestand of map: %s"

#: notebook/notebookapp.py:1141
msgid "Notebook servers are configured to only be run with a password."
msgstr ""
"Notebookservers zijn geconfigureerd om alleen met een wachtwoord te worden "
"uitgevoerd."

#: notebook/notebookapp.py:1142
msgid "Hint: run the following command to set a password"
msgstr "Tip: voer de volgende opdracht uit om een wachtwoord in te stellen"

#: notebook/notebookapp.py:1143
msgid "\t$ python -m notebook.auth password"
msgstr "\t$ python -m notebook.auth wachtwoord"

#: notebook/notebookapp.py:1181
#, python-format
msgid "The port %i is already in use, trying another port."
msgstr "De port %i is al in gebruik, proberen een andere port."

#: notebook/notebookapp.py:1184
#, python-format
msgid "Permission to listen on port %i denied"
msgstr "Toestemming om te luisteren op port %i geweigerd"

#: notebook/notebookapp.py:1193
msgid ""
"ERROR: the notebook server could not be started because no available port "
"could be found."
msgstr ""
"FOUT: de notebookserver kan niet worden gestart omdat er geen beschikbare "
"port kon worden gevonden."

#: notebook/notebookapp.py:1199
msgid "[all ip addresses on your system]"
msgstr "[alle IP-adressen op uw systeem]"

#: notebook/notebookapp.py:1223
#, python-format
msgid "Terminals not available (error was %s)"
msgstr "Terminals niet beschikbaar (fout was %s)"

#: notebook/notebookapp.py:1259
msgid "interrupted"
msgstr "onderbroken"

#: notebook/notebookapp.py:1261
msgid "y"
msgstr "y"

#: notebook/notebookapp.py:1262
msgid "n"
msgstr "n"

#: notebook/notebookapp.py:1263
#, python-format
msgid "Shutdown this notebook server (%s/[%s])? "
msgstr "Deze notebookserver afsluiten (%s/[%s])? "

#: notebook/notebookapp.py:1269
msgid "Shutdown confirmed"
msgstr "Afsluiten bevestigd"

#: notebook/notebookapp.py:1273
msgid "No answer for 5s:"
msgstr "Geen antwoord voor 5s:"

#: notebook/notebookapp.py:1274
msgid "resuming operation..."
msgstr "hervatting van de werking..."

#: notebook/notebookapp.py:1282
#, python-format
msgid "received signal %s, stopping"
msgstr "ontvangen signaal %s, stoppen"

#: notebook/notebookapp.py:1338
#, python-format
msgid "Error loading server extension %s"
msgstr "Foutladen serverextensie %s"

#: notebook/notebookapp.py:1369
#, python-format
msgid "Shutting down %d kernels"
msgstr "%d-kernels afsluiten"

#: notebook/notebookapp.py:1375
#, python-format
msgid "%d active kernel"
msgid_plural "%d active kernels"
msgstr[0] "%d actieve kernel"
msgstr[1] "%d actieve kernel"

#: notebook/notebookapp.py:1379
#, python-format
msgid ""
"The Jupyter Notebook is running at:\n"
"\r"
"%s"
msgstr ""
"De Jupyter Notebook draait op:\n"
"\r"
"%s"

#: notebook/notebookapp.py:1426
msgid "Running as root is not recommended. Use --allow-root to bypass."
msgstr ""
"Hardlopen als root wordt niet aanbevolen. Gebruik --allow-root te "
"omzeilen."

#: notebook/notebookapp.py:1432
msgid ""
"Use Control-C to stop this server and shut down all kernels (twice to skip "
"confirmation)."
msgstr ""
"Gebruik Control-C om deze server te stoppen en sluit alle kernels af (twee "
"keer om bevestiging over te slaan)."

#: notebook/notebookapp.py:1434
msgid ""
"Welcome to Project Jupyter! Explore the various tools available and their "
"corresponding documentation. If you are interested in contributing to the "
"platform, please visit the communityresources section at "
"http://jupyter.org/community.html."
msgstr ""
"Welkom bij Project Jupyter! Bekijk de verschillende tools die beschikbaar "
"zijn en de bijbehorende documentatie. Als je geïnteresseerd bent om bij te "
"dragen aan het platform, ga dan naar de communityresources sectie op "
"http://jupyter.org/community.html."

#: notebook/notebookapp.py:1445
#, python-format
msgid "No web browser found: %s."
msgstr "Geen webbrowser gevonden: %s."

#: notebook/notebookapp.py:1450
#, python-format
msgid "%s does not exist"
msgstr "%s bestaat niet"

#: notebook/notebookapp.py:1484
msgid "Interrupted..."
msgstr "Onderbroken..."

#: notebook/services/contents/filemanager.py:506
#, python-format
msgid "Serving notebooks from local directory: %s"
msgstr "Notebooks uit lokale map activeren: %s"

#: notebook/services/contents/manager.py:68
msgid "Untitled"
msgstr "Naamloos"
