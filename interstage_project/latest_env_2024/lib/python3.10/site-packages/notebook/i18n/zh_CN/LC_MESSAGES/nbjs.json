{"domain": "nbjs", "locale_data": {"nbjs": {"": {"domain": "nbjs"}, "Manually edit the JSON below to manipulate the metadata for this cell.": ["手动编辑下面的 JSON 代码来修改块元数据。"], "Manually edit the JSON below to manipulate the metadata for this notebook.": ["手动编辑下面的 JSON 代码来修改笔记本元数据。"], " We recommend putting custom metadata attributes in an appropriately named substructure, so they don't conflict with those of others.": ["我们建议将自定义的元数据属性放入适当的子结构中，这样就不会与其他的子结构发生冲突。"], "Edit the metadata": ["编辑元数据"], "Edit Notebook Metadata": ["编辑Notebook元数据"], "Edit Cell Metadata": ["编辑单元格元数据"], "Cancel": ["取消"], "Edit": ["编辑"], "OK": ["确定"], "Apply": ["应用"], "WARNING: Could not save invalid JSON.": ["警告: 不能保存无效的JSON。"], "There are no attachments for this cell.": ["这个单元格没有附件。"], "Current cell attachments": ["当前单元格附件"], "Attachments": ["附件"], "Restore": ["重新保存"], "Delete": ["删除"], "Edit attachments": ["编辑附件"], "Edit Notebook Attachments": ["编辑笔记本附件"], "Edit Cell Attachments": ["编辑单元格附件"], "Select a file to insert.": ["选择文件插入"], "Select a file": ["选择文件"], "You are using Jupyter notebook.": ["您正在使用 Jupyter Notebook。"], "The version of the notebook server is: ": ["该 notebook 服务的版本是："], "The server is running on this version of Python:": ["该服务运行中使用的 Python 版本为："], "Waiting for kernel to be available...": ["等待内核可用..."], "Server Information:": ["服务信息："], "Current Kernel Information:": ["当前内核信息："], "Could not access sys_info variable for version information.": ["无法访问 sys_info 变量来获取版本信息。"], "Cannot find sys_info!": ["找不到 sys_info！"], "About Jupyter Notebook": ["关于 Jupyter Notebook"], "unable to contact kernel": ["不能连接到内核"], "toggle rtl layout": ["切换 RTL 布局"], "Toggle the screen directionality between left-to-right and right-to-left": ["切换左至右或右至左的屏幕方向"], "edit command mode keyboard shortcuts": ["编辑命令模式键盘快捷键"], "Open a dialog to edit the command mode keyboard shortcuts": ["打开窗口来编辑快捷键"], "restart kernel": ["重启内核"], "restart the kernel (no confirmation dialog)": ["重启内核（无确认对话框）"], "confirm restart kernel": ["确定重启内核"], "restart the kernel (with dialog)": ["重启内核（带确认对话框）"], "restart kernel and run all cells": ["重启内核并且运行所有单元格"], "restart the kernel, then re-run the whole notebook (no confirmation dialog)": ["重启服务，然后重新运行整个笔记本（无确认对话框）"], "confirm restart kernel and run all cells": ["确认重启内核并且运行所有单元格"], "restart the kernel, then re-run the whole notebook (with dialog)": ["重启内核, 然后重新运行整个notebook（带确认对话框）"], "restart kernel and clear output": ["重启内核并且清空输出"], "restart the kernel and clear all output (no confirmation dialog)": ["重启内核并且清空所有输出（无确认对话框）"], "confirm restart kernel and clear output": ["确认重启内核并且清空输出"], "restart the kernel and clear all output (with dialog)": ["重启内核并且清空所有输出（带确认对话框）"], "interrupt the kernel": ["中断内核"], "run cell and select next": ["运行单元格并且选择下一个单元格"], "run cell, select below": ["运行单元格, 选择下面的单元格"], "run selected cells": ["运行选中的单元格"], "run cell and insert below": ["运行单元格并且在下面插入单元格"], "run all cells": ["运行所有的单元格"], "run all cells above": ["运行上面所有的单元格"], "run all cells below": ["运行下面所有的单元格"], "enter command mode": ["进入命令行模式"], "insert image": ["插入图片"], "cut cell attachments": ["剪切单元格的附件"], "copy cell attachments": ["复制单元格的附件"], "paste cell attachments": ["粘贴单元格的附件"], "split cell at cursor": ["在光标处分割单元格"], "enter edit mode": ["进入编辑模式"], "select previous cell": ["选择上一个单元格"], "select cell above": ["选择上面的单元格"], "select next cell": ["选择下一个单元格"], "select cell below": ["选择下面的单元格"], "extend selection above": ["扩展上面的单元格"], "extend selected cells above": ["扩展上面选择的单元格"], "extend selection below": ["扩展下面的单元格"], "extend selected cells below": ["扩展下面选择的单元格"], "cut selected cells": ["剪切选择的单元格"], "copy selected cells": ["复制选择的单元格"], "paste cells above": ["粘贴到上面"], "paste cells below": ["粘贴到下面"], "insert cell above": ["在上面插入单元格"], "insert cell below": ["在下面插入单元格"], "change cell to code": ["把单元格变成代码快"], "change cell to markdown": ["把单元格变成 Markdown"], "change cell to raw": ["清除单元格格式"], "change cell to heading 1": ["把单元格变成标题 1"], "change cell to heading 2": ["把单元格变成标题 2"], "change cell to heading 3": ["把单元格变成标题 3"], "change cell to heading 4": ["把单元格变成标题 4"], "change cell to heading 5": ["把单元格变成标题 5"], "change cell to heading 6": ["把单元格变成标题 6"], "toggle cell output": ["显示/隐藏单元格输出"], "toggle output of selected cells": ["显示/隐藏选定单元格的输出"], "toggle cell scrolling": ["切换单元格为滚动"], "toggle output scrolling of selected cells": ["切换选中单元格的输出为滚动"], "clear cell output": ["清空所有单元格输出"], "clear output of selected cells": ["清空已选择单元格的输出"], "move cells down": ["下移单元格"], "move selected cells down": ["下移选中单元格"], "move cells up": ["上移单元格"], "move selected cells up": ["上移选中单元格"], "toggle line numbers": ["切换行号"], "show keyboard shortcuts": ["显示键盘快捷键"], "delete cells": ["删除单元格"], "delete selected cells": ["删除选中单元格"], "undo cell deletion": ["撤销删除单元格"], "merge cell with previous cell": ["合并上一个单元格"], "merge cell above": ["合并上面的单元格"], "merge cell with next cell": ["合并下一个单元格"], "merge cell below": ["合并下面的单元格"], "merge selected cells": ["合并选中的单元格"], "merge cells": ["合并单元格"], "merge selected cells, or current cell with cell below if only one cell is selected": ["合并选中单元格, 如果只有一个单元格被选中"], "show command pallette": ["显示命令配置"], "open the command palette": ["打开命令配置"], "toggle all line numbers": ["切换所有行号"], "toggles line numbers in all cells, and persist the setting": ["在所有单元格中切换行号，并保持设置"], "show all line numbers": ["显示行号"], "show line numbers in all cells, and persist the setting": ["在所有单元格中显示行号，并保持设置"], "hide all line numbers": ["隐藏行号"], "hide line numbers in all cells, and persist the setting": ["隐藏行号并保持设置"], "toggle header": ["切换标题"], "switch between showing and hiding the header": ["切换显示和隐藏标题"], "show the header": ["显示标题"], "hide the header": ["隐藏标题"], "toggle toolbar": ["切换工具栏"], "switch between showing and hiding the toolbar": ["切换显示/隐藏工具栏"], "show the toolbar": ["显示工具栏"], "hide the toolbar": ["隐藏工具栏"], "close the pager": ["关闭分页器"], "ignore": ["忽略"], "move cursor up": ["光标上移"], "move cursor down": ["光标下移"], "scroll notebook down": ["向下滚动"], "scroll notebook up": ["向上滚动"], "scroll cell center": ["滚动单元格到中间"], "Scroll the current cell to the center": ["把当前单元格滚动到中间"], "scroll cell top": ["滚动单元格到顶"], "Scroll the current cell to the top": ["将当前单元格滚动到顶部"], "duplicate notebook": ["制作笔记本副本"], "Create and open a copy of the current notebook": ["创建并打开当前笔记本的一个副本"], "trust notebook": ["信任笔记本"], "Trust the current notebook": ["信任当前笔记本"], "rename notebook": ["重命名笔记本"], "Rename the current notebook": ["重命名当前笔记本"], "toggle all cells output collapsed": ["切换折叠所有单元格的输出"], "Toggle the hidden state of all output areas": ["切换所有输出区域的隐藏状态"], "toggle all cells output scrolled": ["切换所有单元格输出的滚动状态"], "Toggle the scrolling state of all output areas": ["切换所有输出区域的滚动状态"], "clear all cells output": ["清空所有单元格输出"], "Clear the content of all the outputs": ["清空所有的输出内容"], "save notebook": ["保存笔记本"], "Save and Checkpoint": ["保存并建立检查点"], "Warning: accessing Cell.cm_config directly is deprecated.": ["警告: 直接访问 Cell.cm_config 已经被弃用了。"], "Unrecognized cell type: %s": ["未知的单元格类型: %s"], "Unrecognized cell type": ["未知的单元格类型"], "Error in cell toolbar callback %s": ["工具栏调用 %s 出现错误"], "Clipboard types: %s": ["剪贴板类型: %s"], "Dialog for paste from system clipboard": ["从系统剪切板粘贴"], "Ctrl-V": [""], "Cmd-V": [""], "Press %s again to paste": ["再按一次 %s 来粘贴"], "Why is this needed? ": ["为什么需要它?"], "We can't get paste events in this browser without a text box. ": ["在浏览器里没有文本框我们不能粘贴. "], "There's an invisible text box focused in this dialog.": ["在这个对话框中有一个不可见的文本框."], "%s to paste": ["%s 来粘贴"], "Can't execute cell since kernel is not set.": ["当前不能执行单元格代码，因为内核还没有准备好。"], "In": [""], "Could not find a kernel matching %s. Please select a kernel:": ["找不到匹配 %s 的内核。请选择一个内核:"], "Continue Without Kernel": ["无内核继续运行"], "Set Kernel": ["设置内核"], "Kernel not found": ["找不到内核"], "Creating Notebook Failed": ["创建笔记本失败"], "The error was: %s": ["错误： %s"], "Run": ["运行"], "Code": ["代码"], "Markdown": ["<PERSON><PERSON>"], "Raw NBConvert": ["<PERSON><PERSON>"], "Heading": ["标题"], "unrecognized cell type:": ["未识别的单元格类型："], "Failed to retrieve MathJax from '%s'": ["未能从 '%s' 中检索 MathJax"], "Math/LaTeX rendering will be disabled.": ["Math/LaTeX 渲染将被禁用。"], "Trusted Notebook": ["可信的笔记本"], "Trust Notebook": ["信任笔记本"], "None": ["无"], "No checkpoints": ["没有检查点"], "Opens in a new window": ["在新窗口打开"], "Autosave in progress, latest changes may be lost.": ["自动保存进行中，最新的改变可能会丢失。"], "Unsaved changes will be lost.": ["未保存的修改将会丢失。"], "The Kernel is busy, outputs may be lost.": ["内核正忙，输出也许会丢失。"], "This notebook is version %1$s, but we only fully support up to %2$s.": ["该笔记本使用了版本 %1$s，但是我们只支持到 %2$s."], "You can still work with this notebook, but cell and output types introduced in later notebook versions will not be available.": ["您仍然可以使用该笔记本，但是在新版本中引入的单元和输出类型将不可用。"], "Restart and Run All Cells": ["重启并运行所有代码块"], "Restart and Clear All Outputs": ["重启并清空所有输出"], "Restart": ["重启"], "Continue Running": ["继续运行"], "Reload": ["重载"], "Overwrite": ["重写"], "Trust": ["信任"], "Revert": ["恢复"], "Newer Notebook": ["新笔记本"], "Use markdown headings": ["使用 Markdown 标题"], "Jupyter no longer uses special heading cells. Instead, write your headings in Markdown cells using # characters:": ["Jupyter 不再使用特殊的标题单元格。请在 Markdown 单元格中使用 # 字符来写标题："], "## This is a level 2 heading": ["## 这是一个二级标题"], "Restart kernel and re-run the whole notebook?": ["重新启动内核并重新运行整个笔记本？"], "Are you sure you want to restart the current kernel and re-execute the whole notebook?  All variables and outputs will be lost.": ["您确定要重新启动当前的内核并重新执行整个笔记本吗？所有的变量和输出都将丢失。"], "Restart kernel and clear all output?": ["重启内核并且清空输出？"], "Do you want to restart the current kernel and clear all output?  All variables and outputs will be lost.": ["您是否希望重新启动当前的内核并清除所有输出？所有的变量和输出都将丢失。"], "Restart kernel?": ["重启内核？"], "Do you want to restart the current kernel?  All variables will be lost.": ["如果重启内核，所有变量都会丢失。是否重启？"], "Shutdown kernel?": ["关闭内核？"], "Do you want to shutdown the current kernel?  All variables will be lost.": ["如果关闭内核，所有变量都会丢失。是否关闭？"], "Notebook changed": ["笔记本改变了"], "The notebook file has changed on disk since the last time we opened or saved it. Do you want to overwrite the file on disk with the version open here, or load the version on disk (reload the page)?": ["自从上次我们打开或保存它以来，笔记本文件已经在磁盘上发生了变化。您希望用这里打开的版本覆盖磁盘上的版本，还是加载磁盘上的版本（刷新页面）？"], "Notebook validation failed": ["Notebook 校验失败"], "The save operation succeeded, but the notebook does not appear to be valid. The validation error was:": ["保存操作成功了，但是这个笔记本看起来并不有效。校验错误："], "A trusted Jupyter notebook may execute hidden malicious code when you open it. Selecting trust will immediately reload this notebook in a trusted state. For more information, see the Jupyter security documentation: ": ["当你打开一个可信任的 Jupyter 笔记本时，它可能会执行隐藏的恶意代码。选择信任将立即在一个可信的状态中重新加载这个笔记本。要了解更多信息，请参阅 Jupyter 安全文档："], "here": ["这里"], "Trust this notebook?": ["信任这个笔记本？"], "Notebook failed to load": ["笔记本加载失败"], "The error was: ": ["错误: "], "See the error console for details.": ["有关详细信息，请参阅错误控制台。"], "The notebook also failed validation:": ["这个笔记本校验也失败了:"], "An invalid notebook may not function properly. The validation error was:": ["无效的笔记本可能无法正常运行。校验错误："], "This notebook has been converted from an older notebook format to the current notebook format v(%s).": ["本笔记本已从较旧的笔记本格式转换为当前的笔记本格式 v(%s)。"], "This notebook has been converted from a newer notebook format to the current notebook format v(%s).": ["这个笔记本已经从一种新的笔记本格式转换为当前的笔记本格式 v(%s)。"], "The next time you save this notebook, the current notebook format will be used.": ["下次你保存这个笔记本时，当前的笔记本格式将会被使用。"], "Older versions of Jupyter may not be able to read the new format.": ["旧版本的 Jupyter 可能无法读取新格式。"], "Some features of the original notebook may not be available.": ["原笔记本的一些特性可能无法使用。"], "To preserve the original version, close the notebook without saving it.": ["为了保存原始版本，关闭笔记本而不保存它。"], "Notebook converted": ["已转换笔记本"], "(No name)": ["（没有名字）"], "An unknown error occurred while loading this notebook. This version can load notebook formats %s or earlier. See the server log for details.": ["加载本笔记本时出现了一个未知的错误。这个版本可以加载 %s 或更早的笔记本。有关详细信息，请参阅服务器日志。"], "Error loading notebook": ["加载笔记本出错"], "Are you sure you want to revert the notebook to the latest checkpoint?": ["确定将笔记本恢复至最近的检查点？"], "This cannot be undone.": ["该操作不能被还原。"], "The checkpoint was last updated at:": ["笔记本的最新检查点更新于："], "Revert notebook to checkpoint": ["恢复笔记本至检查点"], "Edit Mode": ["编辑模式"], "Command Mode": ["命令模式"], "Kernel Created": ["内核已创建"], "Connecting to kernel": ["正在连接内核"], "Not Connected": ["未连接"], "click to reconnect": ["点击重连"], "Restarting kernel": ["重启内核"], "Kernel Restarting": ["内核正在重启"], "The kernel appears to have died. It will restart automatically.": ["内核似乎挂掉了，它很快将自动重启。"], "Dead kernel": ["挂掉的内核"], "Kernel Dead": ["内核挂掉"], "Interrupting kernel": ["正在中断内核"], "No Connection to Kernel": ["没有连接到内核"], "A connection to the notebook server could not be established. The notebook will continue trying to reconnect. Check your network connection or notebook server configuration.": ["无法建立到笔记本服务器的连接。 我们会继续尝试重连。请检查网络连接还有服务配置。"], "Connection failed": ["连接失败"], "No kernel": ["没有内核"], "Kernel is not running": ["内核没有运行"], "Don't Restart": ["不要重启"], "Try Restarting Now": ["现在尝试重启"], "The kernel has died, and the automatic restart has failed. It is possible the kernel cannot be restarted. If you are not able to restart the kernel, you will still be able to save the notebook, but running code will no longer work until the notebook is reopened.": ["内核已经死亡，自动重启也失败了。可能是内核不能重新启动。如果您不能重新启动内核，您仍然能够保存笔记本，但笔记本要重新打开才能运行代码。"], "No Kernel": ["没有内核"], "Failed to start the kernel": ["启动内核失败"], "Kernel Busy": ["内核正忙"], "Kernel starting, please wait...": ["内核正在启动,请等待..."], "Kernel Idle": ["内核空闲"], "Kernel ready": ["内核就绪"], "Using kernel: ": ["使用内核："], "Only candidate for language: %1$s was %2$s.": ["只支持语言： %1$s - %2$s."], "Loading notebook": ["加载笔记本"], "Notebook loaded": ["笔记本已加载"], "Saving notebook": ["保存笔记本"], "Notebook saved": ["笔记本已保存"], "Notebook save failed": ["笔记本保存失败"], "Notebook copy failed": ["笔记本复制失败"], "Checkpoint created": ["检查点已创建"], "Checkpoint failed": ["检查点创建失败"], "Checkpoint deleted": ["检查点已删除"], "Checkpoint delete failed": ["检查点删除失败"], "Restoring to checkpoint...": ["正在恢复至检查点..."], "Checkpoint restore failed": ["检查点恢复失败"], "Autosave disabled": ["自动保存失败"], "Saving every %d sec.": ["每隔 %s 秒保存一次。"], "Trusted": ["可信"], "Not Trusted": ["不可信"], "click to expand output": ["点击展开输出"], "click to expand output; double click to hide output": ["点击展开输出；双击隐藏输出"], "click to unscroll output; double click to hide": ["单击取消滚动输出；双击隐藏"], "click to scroll output; double click to hide": ["点击滚动输出；双击隐藏"], "Javascript error adding output!": ["添加输出时 Javascript 出错了！"], "See your browser Javascript console for more details.": ["更多细节请参见您的浏览器 Javascript 控制台。"], "Out[%d]:": [""], "Unrecognized output: %s": ["未识别的输出： %s"], "Open the pager in an external window": ["在外部窗口打开分页器"], "Close the pager": ["关闭分页器"], "Jupyter Pager": ["<PERSON><PERSON><PERSON> 分页器"], "go to cell start": ["跳到单元格起始处"], "go to cell end": ["跳到单元格最后"], "go one word left": ["往左跳一个单词"], "go one word right": ["往右跳一个单词"], "delete word before": ["删除前面的单词"], "delete word after": ["删除后面的单词"], "redo": ["重做"], "redo selection": ["重新选择"], "emacs-style line kill": ["Emacs 风格的 Line Kill"], "delete line left of cursor": ["删除光标左边一行"], "delete line right of cursor": ["删除光标右边一行"], "code completion or indent": ["代码补全或缩进"], "tooltip": ["工具提示"], "indent": ["缩进"], "dedent": ["取消缩进"], "select all": ["全选"], "undo": ["撤销"], "comment": ["注释"], "delete whole line": ["删除整行"], "undo selection": ["撤销选择"], "toggle overwrite flag": ["切换重写标志"], "Shift": ["Shift"], "Alt": ["Alt"], "Up": ["上"], "Down": ["下"], "Left": ["左"], "Right": ["右"], "Tab": ["Tab"], "Caps Lock": ["大写锁定"], "Esc": ["Esc"], "Ctrl": ["Ctrl"], "Enter": ["Enter"], "Page Up": ["上一页"], "Page Down": ["下一页"], "Home": ["Home"], "End": ["End"], "Space": ["空格"], "Backspace": ["退格"], "Minus": [""], "PageUp": ["上一页"], "The Jupyter Notebook has two different keyboard input modes.": ["Jupyter 笔记本有两种不同的键盘输入模式。"], "<b>Edit mode</b> allows you to type code or text into a cell and is indicated by a green cell border.": ["<b>编辑模式</b>允许您将代码或文本输入到一个单元格中，并通过一个绿色边框的单元格来表示"], "<b>Command mode</b> binds the keyboard to notebook level commands and is indicated by a grey cell border with a blue left margin.": ["<b>命令模式</b>将键盘与笔记本级命令绑定在一起，并通过一个灰框、左边距蓝色的单元格显示。"], "Close": ["关闭"], "Keyboard shortcuts": ["键盘快捷键"], "Command": ["命令"], "Control": ["控制"], "Option": ["选项"], "Return": ["返回"], "Command Mode (press %s to enable)": ["命令行模式（按 %s 生效）"], "Edit Shortcuts": ["编辑快捷键"], "edit command-mode keyboard shortcuts": ["编辑命令模式键盘快捷键"], "Edit Mode (press %s to enable)": ["编辑模式（按 %s 生效）"], "Autosave Failed!": ["自动保存失败！"], "Rename": ["重命名"], "Enter a new notebook name:": ["请输入新的笔记本名称:"], "Rename Notebook": ["重命名笔记本"], "Invalid notebook name. Notebook names must have 1 or more characters and can contain any characters except :/\\. Please enter a new notebook name:": ["无效的笔记本名称。笔记本名称不能为空，并且不能包含\":/.\"。请输入一个新的笔记本名称:"], "Renaming...": ["正在重命名…"], "Unknown error": ["未知错误"], "no checkpoint": ["没有检查点"], "Last Checkpoint: %s": ["最新检查点: %s "], "(unsaved changes)": ["（更改未保存）"], "(autosaved)": ["（已自动保存）"], "Warning: too many matches (%d). Some changes might not be shown or applied.": ["警告：太多的匹配(%d)。有些更改可能不会被显示或应用."], "%d match": ["%d 匹配", "%d 匹配"], "More than 100 matches, aborting": ["超过 100 个匹配, 中止"], "Use regex (JavaScript regex syntax)": ["使用正则表达式（JavaScript 正则表达式语法）"], "Replace in selected cells": ["在选中单元格中替换"], "Match case": ["匹配大小写"], "Find": ["查找"], "Replace": ["替换"], "No matches, invalid or empty regular expression": ["无匹配，表达式无效或表达式为空"], "Replace All": ["全部替换"], "Find and Replace": ["查找并且替换"], "find and replace": ["查找并且替换"], "Write raw LaTeX or other formats here, for use with nbconvert. It will not be rendered in the notebook. When passing through nbconvert, a Raw Cell's content is added to the output unmodified.": ["在这里直接写 LaTeX 或者其它格式的文本来配合 nbconvert。笔记本不会渲染它。传给 nbconvert 时，原始单元格的内容会被完好地加进输出。"], "Grow the tooltip vertically (press shift-tab twice)": ["纵向展开工具提示（按两次 Shift+Tab）"], "show the current docstring in pager (press shift-tab 4 times)": ["在分页器中显示当前的文档字符串（按四次 Shift+Tab）"], "Open in Pager": ["在分页器中打开"], "Tooltip will linger for 10 seconds while you type": ["当您键入时，工具提示会停留十秒"], "Welcome to the Notebook Tour": ["欢迎来到 Notebook 导览"], "You can use the left and right arrow keys to go backwards and forwards.": ["你可以使用左右箭头键来前后移动"], "Filename": ["文件名"], "Click here to change the filename for this notebook.": ["点击这里修改笔记本的文件名"], "Notebook Menubar": ["笔记本菜单栏"], "The menubar has menus for actions on the notebook, its cells, and the kernel it communicates with.": ["菜单栏上的菜单可以用来操作笔记本、单元格和与笔记本通信的内核。"], "Notebook Toolbar": ["笔记本工具栏"], "The toolbar has buttons for the most common actions. Hover your mouse over each button for more information.": ["工具栏有最常见操作的按钮。将鼠标悬停在每个按钮上以获得更多信息。"], "Mode Indicator": ["模式指示器"], "The Notebook has two modes: Edit Mode and Command Mode. In this area, an indicator can appear to tell you which mode you are in.": ["笔记本有两种模式：编辑模式和命令模式。在这个区域，一个指示器可以显示你在哪个模式。"], "Right now you are in Command Mode, and many keyboard shortcuts are available. In this mode, no icon is displayed in the indicator area.": ["现在你处于命令模式，许多快捷键都可以使用。在该模式下，指示区域中没有显示图标。"], "Pressing <code>Enter</code> or clicking in the input text area of the cell switches to Edit Mode.": ["按下<code>Enter</code>或者点击输入文本区域来切换到编辑模式. "], "Notice that the border around the currently active cell changed color. Typing will insert text into the currently active cell.": ["您会发现当前活动单元格周围的边框改变了颜色。键入将在当前活动单元格中插入文本."], "Back to Command Mode": ["回到命令模式"], "Pressing <code>Esc</code> or clicking outside of the input text area takes you back to Command Mode.": ["按下<code>Esc</code>或者点击输入框外面来返回到命令模式。"], "Keyboard Shortcuts": ["键盘快捷键"], "You can click here to get a list of all of the keyboard shortcuts.": ["点击这里获得所有键盘快捷键"], "Kernel Indicator": ["内核指示器"], "This is the Kernel indicator. It looks like this when the Kernel is idle.": ["这是内核指示器。当内核空闲时，它看起来就像这样。"], "The Kernel indicator looks like this when the Kernel is busy.": ["内核指示器在内核繁忙时看起来是这样的。"], "Interrupting the Kernel": ["内核中断"], "To cancel a computation in progress, you can click here.": ["要取消正在进行的计算任务，您可以点击这里。"], "Notification Area": ["任务栏通知区"], "Messages in response to user actions (Save, Interrupt, etc.) appear here.": ["响应用户操作（保存，中断等）的消息出现在这里。"], "End of Tour": ["结束导览"], "This concludes the Jupyter Notebook User Interface Tour.": ["Jupyter 笔记本用户界面之旅到此为止。"], "Edit Attachments": ["编辑附件"], "Cell": ["单元格"], "Edit Metadata": ["编辑元数据"], "Custom": ["自定义"], "Set the MIME type of the raw cell:": ["设置原始单元格的 MIME 类型："], "Raw Cell MIME Type": ["原始单元格的 MIME 类型"], "Raw NBConvert Format": ["原始 NBConvert 类型"], "Raw Cell Format": ["原始单元格格式"], "Slide": ["幻灯片"], "Sub-Slide": ["子幻灯片"], "Fragment": ["碎片"], "Skip": ["跳过"], "Notes": ["代码"], "Slide Type": ["幻灯片类型"], "Slideshow": ["幻灯片"], "Add tag": ["添加标签"], "Edit the list of tags below. All whitespace is treated as tag separators.": ["编辑下面的标签列表。所有空格都被当作标记分隔符。"], "Edit the tags": ["编辑标签"], "Edit Tags": ["编辑标签"], "Shutdown": ["关闭"], "Create a new notebook with %s": ["创建新的笔记本 %s"], "An error occurred while creating a new notebook.": ["创建新笔记本时出错。"], "Creating File Failed": ["创建文件失败"], "An error occurred while creating a new file.": ["创建新文件时出错。"], "Creating Folder Failed": ["创建文件夹失败"], "An error occurred while creating a new folder.": ["创建新文件夹时出错。"], "Failed to read file": ["读取文件失败"], "Failed to read file %s": ["读取文件 %s 失败了"], "The file size is %d MB. Do you still want to upload it?": ["文件大小为 %d MB，依然上传?"], "Large file size warning": ["请注意文件大小"], "Server error: ": ["服务出现错误："], "The notebook list is empty.": ["笔记本列表为空。"], "Click here to rename, delete, etc.": ["点击这里进行重命名或删除等操作"], "Running": ["运行"], "Enter a new file name:": ["请输入一个新的文件名："], "Enter a new directory name:": ["请输入一个新的路径："], "Enter a new name:": ["请输入新名字："], "Rename file": ["文件重命名"], "Rename directory": ["重命名路径"], "Rename notebook": ["重命名笔记本"], "Move": ["移动"], "An error occurred while renaming \"%1$s\" to \"%2$s\".": ["当把 \"%1$s\" 重命名为 \"%2$s\" 时出现错误."], "Rename Failed": ["重命名失败"], "Enter a new destination directory path for this item:": ["为笔记本选择一个新的路径：", "为选中的 %d 笔记本选择一个新的路径："], "Move an Item": ["移动一个文件", "移动 %d 个文件"], "An error occurred while moving \"%1$s\" from \"%2$s\" to \"%3$s\".": ["当把 \"%1$s\" 从 \"%2$s\" 移动到 \"%3$s\" 时出现错误."], "Move Failed": ["移动失败"], "Are you sure you want to permanently delete: \"%s\"?": ["确定永久删除 \"%s\"?", "确定永久删除选中的 %d 个文件或文件夹?"], "An error occurred while deleting \"%s\".": ["当删除 \"%s\" 时, 出现错误。"], "Delete Failed": ["删除失败"], "Are you sure you want to duplicate: \"%s\"?": ["确定制作 \"%s\" 的副本？", "确定制作选中的 %d 个文件的副本?"], "Duplicate": ["制作副本"], "An error occurred while duplicating \"%s\".": ["制作 \"%s\" 的副本时出现错误。"], "Duplicate Failed": ["制作副本失败"], "Upload": ["上传"], "Invalid file name": ["无效的文件名"], "File names must be at least one character and not start with a period": ["文件名不能为空，并且不能以句号开始，除下划线以外的符号都不能开头"], "Cannot upload invalid Notebook": ["无法上传无效的笔记本"], "There is already a file named \"%s\". Do you want to replace it?": ["已经存在一个名为 \"%s\" 的文件，替换现有文件？"], "Replace file": ["替换文件"]}}}