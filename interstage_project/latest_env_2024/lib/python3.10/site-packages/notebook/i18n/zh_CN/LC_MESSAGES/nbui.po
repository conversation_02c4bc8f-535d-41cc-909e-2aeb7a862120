# Translations template for Ju<PERSON><PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
# Universal translation: cell=单元格; kernal=内核; don't translate notebook whenever referring to Jupyter Notebook
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-08-25 02:53-0400\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.5.0\n"

#: notebook/templates/404.html:3
msgid "You are requesting a page that does not exist!"
msgstr "您所请求的页面不存在！"

#: notebook/templates/edit.html:37
msgid "current mode"
msgstr "当前模式"

#: notebook/templates/edit.html:48 notebook/templates/notebook.html:78
msgid "File"
msgstr "文件"

#: notebook/templates/edit.html:50 notebook/templates/tree.html:57
msgid "New"
msgstr "新建"

#: notebook/templates/edit.html:51
msgid "Save"
msgstr "保存"

#: notebook/templates/edit.html:52 notebook/templates/tree.html:36
msgid "Rename"
msgstr "重命名"

#: notebook/templates/edit.html:53 notebook/templates/tree.html:38
msgid "Download"
msgstr "下载"

#: notebook/templates/edit.html:56 notebook/templates/notebook.html:131
#: notebook/templates/tree.html:41
msgid "Edit"
msgstr "编辑"

#: notebook/templates/edit.html:58
msgid "Find"
msgstr "查找"

#: notebook/templates/edit.html:59
msgid "Find &amp; Replace"
msgstr "查找 &amp; 替换"

#: notebook/templates/edit.html:61
msgid "Key Map"
msgstr "键值对"

#: notebook/templates/edit.html:62
msgid "Default"
msgstr "默认"

#: notebook/templates/edit.html:63
msgid "Sublime Text"
msgstr "代码编辑器"

#: notebook/templates/edit.html:68 notebook/templates/notebook.html:159
#: notebook/templates/tree.html:40
msgid "View"
msgstr "查看"

#: notebook/templates/edit.html:70 notebook/templates/notebook.html:162
msgid "Show/Hide the logo and notebook title (above menu bar)"
msgstr "显示/隐藏 标题和logo"

#: notebook/templates/edit.html:71 notebook/templates/notebook.html:163
msgid "Toggle Header"
msgstr "显示/隐藏 标题栏"

#: notebook/templates/edit.html:72 notebook/templates/notebook.html:171
msgid "Toggle Line Numbers"
msgstr "显示/隐藏 行号"

#: notebook/templates/edit.html:75
msgid "Language"
msgstr "语言"

#: notebook/templates/error.html:23
msgid "The error was:"
msgstr "错误："

#: notebook/templates/login.html:24
msgid "Password or token:"
msgstr "密码或者token："

#: notebook/templates/login.html:26
msgid "Password:"
msgstr "密码："

#: notebook/templates/login.html:31
msgid "Log in"
msgstr "登录"

#: notebook/templates/login.html:39
msgid "No login available, you shouldn't be seeing this page."
msgstr "还没有登录，请先登录。"

#: notebook/templates/logout.html:31
#, python-format
msgid "Proceed to the <a href=\"%(base_url)s\">dashboard"
msgstr "进入 <a href=\"%(base_url)s\"> 指示板"

#: notebook/templates/logout.html:33
#, python-format
msgid "Proceed to the <a href=\"%(base_url)slogin\">login page"
msgstr "进入 <a href=\"%(base_url)slogin\"> 登录页面"

#: notebook/templates/notebook.html:62
msgid "Menu"
msgstr "菜单"

#: notebook/templates/notebook.html:65 notebook/templates/notebook.html:254
msgid "Kernel"
msgstr "内核"

#: notebook/templates/notebook.html:68
msgid "This notebook is read-only"
msgstr "这个notebook文件是只读的"

#: notebook/templates/notebook.html:81
msgid "New Notebook"
msgstr "新建Notebook"

#: notebook/templates/notebook.html:85
msgid "Opens a new window with the Dashboard view"
msgstr "以仪表盘视角打开新的窗口"

#: notebook/templates/notebook.html:86
msgid "Open..."
msgstr "打开..."

#: notebook/templates/notebook.html:90
msgid "Open a copy of this notebook's contents and start a new kernel"
msgstr "复制并打开当前notebook的内容，并启动一个新的内核"

#: notebook/templates/notebook.html:91
msgid "Make a Copy..."
msgstr "复制..."

#: notebook/templates/notebook.html:92
msgid "Rename..."
msgstr "重命名..."

#: notebook/templates/notebook.html:93
msgid "Save and Checkpoint"
msgstr "保存，且作为一个检查点"

#: notebook/templates/notebook.html:96
msgid "Revert to Checkpoint"
msgstr "恢复到某一个检查点"

#: notebook/templates/notebook.html:106
msgid "Print Preview"
msgstr "打印预览"

#: notebook/templates/notebook.html:107
msgid "Download as"
msgstr "另存为"

#: notebook/templates/notebook.html:109
msgid "Notebook (.ipynb)"
msgstr "Notebook (.ipynb)"

#: notebook/templates/notebook.html:110
msgid "Script"
msgstr "脚本"

#: notebook/templates/notebook.html:111
msgid "HTML (.html)"
msgstr ""

#: notebook/templates/notebook.html:112
msgid "Markdown (.md)"
msgstr ""

#: notebook/templates/notebook.html:113
msgid "reST (.rst)"
msgstr ""

#: notebook/templates/notebook.html:114
msgid "LaTeX (.tex)"
msgstr ""

#: notebook/templates/notebook.html:115
msgid "PDF via LaTeX (.pdf)"
msgstr "通过LaTeX生成的PDF (.pdf)"

#: notebook/templates/notebook.html:118
msgid "Deploy as"
msgstr "部署在"

#: notebook/templates/notebook.html:123
msgid "Trust the output of this notebook"
msgstr "信任当前notebook的输出"

#: notebook/templates/notebook.html:124
msgid "Trust Notebook"
msgstr "信任当前Notebook"

#: notebook/templates/notebook.html:127
msgid "Shutdown this notebook's kernel, and close this window"
msgstr "关闭当前notebook的内核，并关闭当前窗口"

#: notebook/templates/notebook.html:128
msgid "Close and Halt"
msgstr "关闭"

#: notebook/templates/notebook.html:133
msgid "Cut Cells"
msgstr "剪切单元格"

#: notebook/templates/notebook.html:134
msgid "Copy Cells"
msgstr "复制单元格"

#: notebook/templates/notebook.html:135
msgid "Paste Cells Above"
msgstr "粘贴单元格到上面"

#: notebook/templates/notebook.html:136
msgid "Paste Cells Below"
msgstr "粘贴单元格到下面"

#: notebook/templates/notebook.html:137
msgid "Paste Cells &amp; Replace"
msgstr "粘贴单元格 &amp; 替换"

#: notebook/templates/notebook.html:138
msgid "Delete Cells"
msgstr "删除单元格"

#: notebook/templates/notebook.html:139
msgid "Undo Delete Cells"
msgstr "撤销删除单元格"

#: notebook/templates/notebook.html:141
msgid "Split Cell"
msgstr "分割单元格"

#: notebook/templates/notebook.html:142
msgid "Merge Cell Above"
msgstr "合并上面的单元格"

#: notebook/templates/notebook.html:143
msgid "Merge Cell Below"
msgstr "合并下面的单元格"

#: notebook/templates/notebook.html:145
msgid "Move Cell Up"
msgstr "上移单元格"

#: notebook/templates/notebook.html:146
msgid "Move Cell Down"
msgstr "下移单元格"

#: notebook/templates/notebook.html:148
msgid "Edit Notebook Metadata"
msgstr "编辑Notebook的元数据"

#: notebook/templates/notebook.html:150
msgid "Find and Replace"
msgstr "查找并替换"

#: notebook/templates/notebook.html:152
msgid "Cut Cell Attachments"
msgstr "剪切附件"

#: notebook/templates/notebook.html:153
msgid "Copy Cell Attachments"
msgstr "复制附件"

#: notebook/templates/notebook.html:154
msgid "Paste Cell Attachments"
msgstr "粘贴附件"

#: notebook/templates/notebook.html:156
msgid "Insert Image"
msgstr "插入图片"

#: notebook/templates/notebook.html:166
msgid "Show/Hide the action icons (below menu bar)"
msgstr "显示/隐藏 操作图标"

#: notebook/templates/notebook.html:167
msgid "Toggle Toolbar"
msgstr "显示/隐藏 工具栏"

#: notebook/templates/notebook.html:170
msgid "Show/Hide line numbers in cells"
msgstr "显示/隐藏 当前单元格的行号"

#: notebook/templates/notebook.html:174
msgid "Cell Toolbar"
msgstr "单元格 工具栏"

#: notebook/templates/notebook.html:179
msgid "Insert"
msgstr "插入"

#: notebook/templates/notebook.html:182
msgid "Insert an empty Code cell above the currently active cell"
msgstr "在当前代码块上面插入一个空的单元格"

#: notebook/templates/notebook.html:183
msgid "Insert Cell Above"
msgstr "在上面插入一个单元格"

#: notebook/templates/notebook.html:185
msgid "Insert an empty Code cell below the currently active cell"
msgstr "在当前代码块下面插入一个空的单元格"

#: notebook/templates/notebook.html:186
msgid "Insert Cell Below"
msgstr "在下面插入一个单元格"

#: notebook/templates/notebook.html:189
msgid "Cell"
msgstr "单元格"

#: notebook/templates/notebook.html:191
msgid "Run this cell, and move cursor to the next one"
msgstr "运行此单元格，并将光标移到下一个"

#: notebook/templates/notebook.html:192
msgid "Run Cells"
msgstr "运行所有单元格"

#: notebook/templates/notebook.html:193
msgid "Run this cell, select below"
msgstr "运行此单元格，并自动选择下一个"

#: notebook/templates/notebook.html:194
msgid "Run Cells and Select Below"
msgstr "运行所有单元格，并自动选择下一个"

#: notebook/templates/notebook.html:195
msgid "Run this cell, insert below"
msgstr "运行此单元格，并在下面插入一个新的单元格"

#: notebook/templates/notebook.html:196
msgid "Run Cells and Insert Below"
msgstr "运行所有单元格，并在下面插入一个新的单元格"

#: notebook/templates/notebook.html:197
msgid "Run all cells in the notebook"
msgstr "运行所有的单元格"

#: notebook/templates/notebook.html:198
msgid "Run All"
msgstr "运行所有"

#: notebook/templates/notebook.html:199
msgid "Run all cells above (but not including) this cell"
msgstr "运行上面所有的单元格(但不包括这个单元格)"

#: notebook/templates/notebook.html:200
msgid "Run All Above"
msgstr "运行上面所有的单元格"

#: notebook/templates/notebook.html:201
msgid "Run this cell and all cells below it"
msgstr "运行当前及以下所有的单元格"

#: notebook/templates/notebook.html:202
msgid "Run All Below"
msgstr "运行下面的所有的单元格"

#: notebook/templates/notebook.html:205
msgid "All cells in the notebook have a cell type. By default, new cells are created as 'Code' cells"
msgstr "Notebook里的所有单元格都有一个类型。默认新单元格都会被创建为代码单元格"

#: notebook/templates/notebook.html:206
msgid "Cell Type"
msgstr "单元格类型"

#: notebook/templates/notebook.html:209
msgid "Contents will be sent to the kernel for execution, and output will display in the footer of cell"
msgstr "内容将被发送到内核执行，输出将显示在单元格的页脚"

#: notebook/templates/notebook.html:212
msgid "Contents will be rendered as HTML and serve as explanatory text"
msgstr "内容将以HTML形式呈现, 并作为解释性文本"

#: notebook/templates/notebook.html:213 notebook/templates/notebook.html:298
msgid "Markdown"
msgstr ""

#: notebook/templates/notebook.html:215
msgid "Contents will pass through nbconvert unmodified"
msgstr "内容将通过nbconvert且不会被修改"

#: notebook/templates/notebook.html:216
msgid "Raw NBConvert"
msgstr "原生 NBConvert"

#: notebook/templates/notebook.html:220
msgid "Current Outputs"
msgstr "当前输出"

#: notebook/templates/notebook.html:223
msgid "Hide/Show the output of the current cell"
msgstr "隐藏/显示当前单元格输出"

#: notebook/templates/notebook.html:224 notebook/templates/notebook.html:240
msgid "Toggle"
msgstr "显示/隐藏"

#: notebook/templates/notebook.html:227
msgid "Scroll the output of the current cell"
msgstr "滚动到当前单元格的输出"

#: notebook/templates/notebook.html:228 notebook/templates/notebook.html:244
msgid "Toggle Scrolling"
msgstr "滚动显示"

#: notebook/templates/notebook.html:231
msgid "Clear the output of the current cell"
msgstr "清除当前单元格的输出"

#: notebook/templates/notebook.html:232 notebook/templates/notebook.html:248
msgid "Clear"
msgstr "清空"

#: notebook/templates/notebook.html:236
msgid "All Output"
msgstr "所有输出"

#: notebook/templates/notebook.html:239
msgid "Hide/Show the output of all cells"
msgstr "隐藏/显示 所有单元格的输出"

#: notebook/templates/notebook.html:243
msgid "Scroll the output of all cells"
msgstr "切换所有单元格的输出为滚动模式"

#: notebook/templates/notebook.html:247
msgid "Clear the output of all cells"
msgstr "清空所有代码块的输出"

#: notebook/templates/notebook.html:257
msgid "Send Keyboard Interrupt (CTRL-C) to the Kernel"
msgstr "按下 CTRL-C 中断内核"

#: notebook/templates/notebook.html:258
msgid "Interrupt"
msgstr "中断"

#: notebook/templates/notebook.html:261
msgid "Restart the Kernel"
msgstr "重启内核"

#: notebook/templates/notebook.html:262
msgid "Restart"
msgstr "重启"

#: notebook/templates/notebook.html:265
msgid "Restart the Kernel and clear all output"
msgstr "重启服务并清空所有输出"

#: notebook/templates/notebook.html:266
msgid "Restart &amp; Clear Output"
msgstr "重启 &amp; 清空输出"

#: notebook/templates/notebook.html:269
msgid "Restart the Kernel and re-run the notebook"
msgstr "重启内核并且重新运行整个notebook"

#: notebook/templates/notebook.html:270
msgid "Restart &amp; Run All"
msgstr "重启 &amp; 运行所有"

#: notebook/templates/notebook.html:273
msgid "Reconnect to the Kernel"
msgstr "重连内核"

#: notebook/templates/notebook.html:274
msgid "Reconnect"
msgstr "重连"

#: notebook/templates/notebook.html:282
msgid "Change kernel"
msgstr "更换内核"

#: notebook/templates/notebook.html:287
msgid "Help"
msgstr "帮助"

#: notebook/templates/notebook.html:290
msgid "A quick tour of the notebook user interface"
msgstr "快速浏览一下notebook用户界面"

#: notebook/templates/notebook.html:290
msgid "User Interface Tour"
msgstr "用户界面之旅"

#: notebook/templates/notebook.html:291
msgid "Opens a tooltip with all keyboard shortcuts"
msgstr "打开包含所有快捷键的提示信息"

#: notebook/templates/notebook.html:291
msgid "Keyboard Shortcuts"
msgstr "快捷键"

#: notebook/templates/notebook.html:292
msgid "Opens a dialog allowing you to edit Keyboard shortcuts"
msgstr "打开对话框编辑快捷键"

#: notebook/templates/notebook.html:292
msgid "Edit Keyboard Shortcuts"
msgstr "编辑快捷键"

#: notebook/templates/notebook.html:297
msgid "Notebook Help"
msgstr "帮助"

#: notebook/templates/notebook.html:303
msgid "Opens in a new window"
msgstr "在新窗口打开"

#: notebook/templates/notebook.html:319
msgid "About Jupyter Notebook"
msgstr "关于本程序"

#: notebook/templates/notebook.html:319
msgid "About"
msgstr "关于"

#: notebook/templates/page.html:114
msgid "Jupyter Notebook requires JavaScript."
msgstr "Jupyter Notebook 需要 JavaScript。"

#: notebook/templates/page.html:115
msgid "Please enable it to proceed. "
msgstr "请启用它以继续。"

#: notebook/templates/page.html:122
msgid "dashboard"
msgstr "指示板"

#: notebook/templates/page.html:135
msgid "Logout"
msgstr "注销"

#: notebook/templates/page.html:137
msgid "Login"
msgstr "登录"

#: notebook/templates/tree.html:23
msgid "Files"
msgstr "文件"

#: notebook/templates/tree.html:24
msgid "Running"
msgstr "运行"

#: notebook/templates/tree.html:25
msgid "Clusters"
msgstr "集群"

#: notebook/templates/tree.html:32
msgid "Select items to perform actions on them."
msgstr "选择操作对象."

#: notebook/templates/tree.html:35
msgid "Duplicate selected"
msgstr "复制选中的对象"

#: notebook/templates/tree.html:35
msgid "Duplicate"
msgstr "复制"

#: notebook/templates/tree.html:36
msgid "Rename selected"
msgstr "重命名选中的对象"

#: notebook/templates/tree.html:37
msgid "Move selected"
msgstr "移动选中的对象"

#: notebook/templates/tree.html:37
msgid "Move"
msgstr "移动"

#: notebook/templates/tree.html:38
msgid "Download selected"
msgstr "下载选中的对象"

#: notebook/templates/tree.html:39
msgid "Shutdown selected notebook(s)"
msgstr "停止运行选择的notebook(s)"

#: notebook/templates/notebook.html:278
#: notebook/templates/tree.html:39
msgid "Shutdown"
msgstr "关闭"

#: notebook/templates/tree.html:40
msgid "View selected"
msgstr "查看选中的对象"

#: notebook/templates/tree.html:41
msgid "Edit selected"
msgstr "编辑选中的对象"

#: notebook/templates/tree.html:42
msgid "Delete selected"
msgstr "删除选中的对象"

#: notebook/templates/tree.html:50
msgid "Click to browse for a file to upload."
msgstr "浏览文件并上传"

#: notebook/templates/tree.html:51
msgid "Upload"
msgstr "上传"

#: notebook/templates/tree.html:65
msgid "Text File"
msgstr "文本文件"

#: notebook/templates/tree.html:68
msgid "Folder"
msgstr "文件夹"

#: notebook/templates/tree.html:72
msgid "Terminal"
msgstr "终端"

#: notebook/templates/tree.html:76
msgid "Terminals Unavailable"
msgstr "终端不可用"

#: notebook/templates/tree.html:82
msgid "Refresh notebook list"
msgstr "刷新笔记列表"

#: notebook/templates/tree.html:90
msgid "Select All / None"
msgstr "全选 / 全不选"

#: notebook/templates/tree.html:93
msgid "Select..."
msgstr "选择..."

#: notebook/templates/tree.html:98
msgid "Select All Folders"
msgstr "选择所有文件夹"

#: notebook/templates/tree.html:98
msgid "Folders"
msgstr "文件夹"

#: notebook/templates/tree.html:99
msgid "Select All Notebooks"
msgstr "选择所有Notebooks"

#: notebook/templates/tree.html:99
msgid "All Notebooks"
msgstr "所有Notebooks"

#: notebook/templates/tree.html:100
msgid "Select Running Notebooks"
msgstr "选择运行中的Notebooks"

#: notebook/templates/tree.html:100
msgid "Running"
msgstr "运行"

#: notebook/templates/tree.html:101
msgid "Select All Files"
msgstr "选择所有文件"

#: notebook/templates/tree.html:101
msgid "Files"
msgstr "文件"

#: notebook/templates/tree.html:114
msgid "Last Modified"
msgstr "最后修改"

#: notebook/templates/tree.html:120
msgid "Name"
msgstr "名字"

#: notebook/templates/tree.html:130
msgid "Currently running Jupyter processes"
msgstr "当前运行Jupyter"

#: notebook/templates/tree.html:134
msgid "Refresh running list"
msgstr "刷新运行列表"

#: notebook/templates/tree.html:150
msgid "There are no terminals running."
msgstr "没有终端正在运行。"

#: notebook/templates/tree.html:152
msgid "Terminals are unavailable."
msgstr "终端不可用。"

#: notebook/templates/tree.html:162
msgid "Notebooks"
msgstr ""

#: notebook/templates/tree.html:169
msgid "There are no notebooks running."
msgstr "没有notebooks正在运行。"

#: notebook/templates/tree.html:178
msgid "Clusters tab is now provided by IPython parallel."
msgstr "集群标签现在由IPython并行提供。"

#: notebook/templates/tree.html:179
msgid "See '<a href=\"https://github.com/ipython/ipyparallel\">IPython parallel</a>' for installation details."
msgstr "安装细节查看 '<a href=\"https://github.com/ipython/ipyparallel\">IPython parallel</a>'。"

