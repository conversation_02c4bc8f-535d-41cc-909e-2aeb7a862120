# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-07-08 21:52-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

#: notebook/notebookapp.py:53
msgid "The Jupyter Notebook requires tornado >= 4.0"
msgstr "Jupyter Notebook は tornade 4.0 以上が必要です"

#: notebook/notebookapp.py:57
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have < 1.1.0"
msgstr "Jupyter Notebook は tornade 4.0 以上が必要ですが 1.1.0 以下です"

#: notebook/notebookapp.py:59
#, python-format
msgid "The Jupyter Notebook requires tornado >= 4.0, but you have %s"
msgstr "Jupyter Notebook は tornade 4.0 以上が必要ですが %s です"

#: notebook/notebookapp.py:209
msgid "The `ignore_minified_js` flag is deprecated and no longer works."
msgstr "`ignore_minified_js` フラグは非推奨であり既に動作していません。"

#: notebook/notebookapp.py:210
#, python-format
msgid "Alternatively use `%s` when working on the notebook's Javascript and LESS"
msgstr "ノートブックの Javascript と LESS で動作する場合には代わりに `%s` を使用してください。"

#: notebook/notebookapp.py:211
msgid "The `ignore_minified_js` flag is deprecated and will be removed in Notebook 6.0"
msgstr "`ignore_minified_js` フラグは非推奨でありノートブック 6.0 では削除されます"

#: notebook/notebookapp.py:389
msgid "List currently running notebook servers."
msgstr "現在起動中のノートブックサーバの一覧"

#: notebook/notebookapp.py:393
msgid "Produce machine-readable JSON output."
msgstr "機械で読み込み可能な JSON 出力。"

#: notebook/notebookapp.py:397
msgid "If True, each line of output will be a JSON object with the details from the server info file."
msgstr "True の場合、出力の各行はサーバ情報ファイルからの詳細情報を含む JSON オブジェクトになります。"

#: notebook/notebookapp.py:402
msgid "Currently running servers:"
msgstr "現在実行中のサーバ:"

#: notebook/notebookapp.py:419
msgid "Don't open the notebook in a browser after startup."
msgstr "起動後にブラウザでノートブックを開かない。"

#: notebook/notebookapp.py:423
msgid "DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib."
msgstr "無効: matplotlib を有効にするにはノートブックで %pylab または %matplotlib を使用して下さい。"

#: notebook/notebookapp.py:439
msgid "Allow the notebook to be run from root user."
msgstr "ノートブックをrootユーザーから実行できるようにする。"

#: notebook/notebookapp.py:470
msgid ""
"The Jupyter HTML Notebook.\n"
"    \n"
"    This launches a Tornado based HTML Notebook Server that serves up an HTML5/Javascript Notebook client."
msgstr ""
"The Jupyter HTML Notebook.\n"
"    \n"
"    HTML5/Javascript Notebook クライアントを提供する Tornado ベースの HTML Notebook サーバを起動します。"

#: notebook/notebookapp.py:509
msgid "Deprecated: Use minified JS file or not, mainly use during dev to avoid JS recompilation"
msgstr "非推奨: 圧縮された JS ファイルを使用するかどうか。主に開発中に JS が再コンパイルされるのを回避するために使用します。"

#: notebook/notebookapp.py:540
msgid "Set the Access-Control-Allow-Credentials: true header"
msgstr "Access-Control-Allow-Credentials: true ヘッダーを設定します。"

#: notebook/notebookapp.py:544
msgid "Whether to allow the user to run the notebook as root."
msgstr "ユーザーがノートブックを root として実行できるようにするかどうか。"

#: notebook/notebookapp.py:548
msgid "The default URL to redirect to from `/`"
msgstr "`/` からリダイレクトされるデフォルトの URL"

#: notebook/notebookapp.py:552
msgid "The IP address the notebook server will listen on."
msgstr "ノートブックサーバが待ち受ける IP アドレス。"

#: notebook/notebookapp.py:565
#, python-format
msgid ""
"Cannot bind to localhost, using 127.0.0.1 as default ip\n"
"%s"
msgstr ""
"localhost でバインドできません。デフォルト IP アドレスとして 127.0.0.1 を使用します\n"
"%s"

#: notebook/notebookapp.py:579
msgid "The port the notebook server will listen on."
msgstr "ノートブックサーバが待ち受けするポート番号。"

#: notebook/notebookapp.py:583
msgid "The number of additional ports to try if the specified port is not available."
msgstr "指定されたポートが利用できない場合に試す追加のポートの数。"

#: notebook/notebookapp.py:587
msgid "The full path to an SSL/TLS certificate file."
msgstr "SSL/TLS 証明書ファイルへの完全なパス。"

#: notebook/notebookapp.py:591
msgid "The full path to a private key file for usage with SSL/TLS."
msgstr "SSL/TLS で使用する秘密鍵ファイルへの完全なパス。"

#: notebook/notebookapp.py:595
msgid "The full path to a certificate authority certificate for SSL/TLS client authentication."
msgstr "SSL/TLS クライアント認証用の認証局証明書への完全なパス。"

#: notebook/notebookapp.py:599
msgid "The file where the cookie secret is stored."
msgstr "cookie secret を保存するファイル。"

#: notebook/notebookapp.py:628
#, python-format
msgid "Writing notebook server cookie secret to %s"
msgstr "ノートブックサーバは cookie secret を %s に書き込みます"

#: notebook/notebookapp.py:635
#, python-format
msgid "Could not set permissions on %s"
msgstr "%s の権限を設定出来ませんでした"

#: notebook/notebookapp.py:640
msgid ""
"Token used for authenticating first-time connections to the server.\n"
"\n"
"        When no password is enabled,\n"
"        the default is to generate a new, random token.\n"
"\n"
"        Setting to an empty string disables authentication altogether, which is NOT RECOMMENDED.\n"
"        "
msgstr ""
"サーバに接続する際に初回の認証に使われるトークン。\n"
"\n"
"        パスワード無しが有効になっている場合\n"
"        デフォルト値はランダムなトークンが新しく生成されます。\n"
"\n"
"        空の文字列に設定すると認証が完全に無効になります。これは推奨されていません。\n"
"        "

#: notebook/notebookapp.py:650
msgid ""
"One-time token used for opening a browser.\n"
"        Once used, this token cannot be used again.\n"
"        "
msgstr ""
"開いたブラウザが仕様するワンタイムトークン。\n"
"        1度使用されると再度使用する事が出来ません。\n"
"        "

#: notebook/notebookapp.py:726
msgid ""
"Specify Where to open the notebook on startup. This is the\n"
"        `new` argument passed to the standard library method `webbrowser.open`.\n"
"        The behaviour is not guaranteed, but depends on browser support. Valid\n"
"        values are:\n"
"            2 opens a new tab,\n"
"            1 opens a new window,\n"
"            0 opens in an existing window.\n"
"        See the `webbrowser.open` documentation for details.\n"
"        "
msgstr ""
"起動時にどこでノートブックを開くかを指定します。これは\n"
"        標準ライブラリのメソッド `webbrowser.open` の引数 `new` に渡されます。\n"
"        動作は保証されていませんがブラウザのサポートによって異なります。\n"
"        有効な値:\n"
"            2 新しいタブで開く\n"
"            1 新しいウィンドウで開く\n"
"            0 既にあるウィンドウで開く\n"
"        詳細は `webbrowser.open` のドキュメントを参照。\n"
"        "

#: notebook/notebookapp.py:737
msgid "DEPRECATED, use tornado_settings"
msgstr "非推奨 tornado_settings の使用"

#: notebook/notebookapp.py:742
msgid ""
"\n"
"    webapp_settings is deprecated, use tornado_settings.\n"
msgstr ""
"\n"
"    webapp_settings は非推奨です。tornado_settings を使って下さい。\n"

#: notebook/notebookapp.py:746
msgid "Supply overrides for the tornado.web.Application that the Jupyter notebook uses."
msgstr "Jupyterノートブックが使用する tornado.web.Application のオーバーライドを指定します。"

#: notebook/notebookapp.py:750
msgid ""
"\n"
"        Set the tornado compression options for websocket connections.\n"
"\n"
"        This value will be returned from :meth:`WebSocketHandler.get_compression_options`.\n"
"        None (default) will disable compression.\n"
"        A dict (even an empty one) will enable compression.\n"
"\n"
"        See the tornado docs for WebSocketHandler.get_compression_options for details.\n"
"        "
msgstr ""
"\n"
"        tornado の websocket 接続の圧縮オプションを指定します。\n"
"\n"
"        この値は :meth:`WebSocketHandler.get_compression_options` から返されます。\n"
"        None (default) の場合は圧縮は無効になります。\n"
"        辞書 (空でも良い) の場合は圧縮が有効になります。\n"
"\n"
"        詳細は tornado の WebSocketHandler.get_compression_options のドキュメントを参照。\n"
"        "

#: notebook/notebookapp.py:761
msgid "Supply overrides for terminado. Currently only supports \"shell_command\"."
msgstr "terminado のオーバーライドを指定します。現時は \"shell_command \" のみをサポートしています。"

#: notebook/notebookapp.py:764
msgid "Extra keyword arguments to pass to `set_secure_cookie`. See tornado's set_secure_cookie docs for details."
msgstr "`set_secure_cookie` に渡す追加のキーワード引数。詳細は tornado の set_secure_cookie のドキュメントを参照。"

#: notebook/notebookapp.py:768
msgid ""
"Supply SSL options for the tornado HTTPServer.\n"
"            See the tornado docs for details."
msgstr ""
"tornado HTTPServer の SSL オプションを指定します。\n"
"            詳しくは tornado のドキュメントを参照。"

#: notebook/notebookapp.py:772
msgid "Supply extra arguments that will be passed to Jinja environment."
msgstr "Jinja environment に渡される追加の引数を指定します。"

#: notebook/notebookapp.py:776
msgid "Extra variables to supply to jinja templates when rendering."
msgstr "jinja テンプレートがレンダリングする際に渡される追加の変数。"

#: notebook/notebookapp.py:812
msgid "DEPRECATED use base_url"
msgstr "非推奨 base_url の使用"

#: notebook/notebookapp.py:816
msgid "base_project_url is deprecated, use base_url"
msgstr "base_project_url は非推奨です。base_url を使用して下さい。"

#: notebook/notebookapp.py:832
msgid "Path to search for custom.js, css"
msgstr "custom.js、CSS を検索するためのパス"

#: notebook/notebookapp.py:844
msgid ""
"Extra paths to search for serving jinja templates.\n"
"\n"
"        Can be used to override templates from notebook.templates."
msgstr ""
"Jinja テンプレートを探す為の追加パス。\n"
"\n"
"        notebook.templates を上書きする為に使う事が出来ます。"

#: notebook/notebookapp.py:855
msgid "extra paths to look for Javascript notebook extensions"
msgstr "Javascript ノートブック拡張への追加パス"

#: notebook/notebookapp.py:900
#, python-format
msgid "Using MathJax: %s"
msgstr "使用している MathJax: %s"

#: notebook/notebookapp.py:903
msgid "The MathJax.js configuration file that is to be used."
msgstr "使用される MathJax.js 設定ファイル。"

#: notebook/notebookapp.py:908
#, python-format
msgid "Using MathJax configuration file: %s"
msgstr "使用する MathJax 設定ファイル: %s"

#: notebook/notebookapp.py:914
msgid "The notebook manager class to use."
msgstr "ノートブックマネージャのクラス"

#: notebook/notebookapp.py:920
msgid "The kernel manager class to use."
msgstr "カーネルマネージャのクラス"

#: notebook/notebookapp.py:926
msgid "The session manager class to use."
msgstr "セッションマネージャのクラス"

#: notebook/notebookapp.py:932
msgid "The config manager class to use"
msgstr "設定マネージャのクラス"

#: notebook/notebookapp.py:953
msgid "The login handler class to use."
msgstr "ログインのハンドラクラス"

#: notebook/notebookapp.py:960
msgid "The logout handler class to use."
msgstr "ログアウトのハンドラクラス"

#: notebook/notebookapp.py:964
msgid "Whether to trust or not X-Scheme/X-Forwarded-Proto and X-Real-Ip/X-Forwarded-For headerssent by the upstream reverse proxy. Necessary if the proxy handles SSL"
msgstr "X-Scheme/X-Forwarded-Proto および X-Real-Ip/X-Forwarded-For ヘッダーがアップストリームのリバースプロキシによって送信されたことを信頼するかどうか。プロキシが SSL を処理する場合に必要となります。"

#: notebook/notebookapp.py:976
msgid ""
"\n"
"        DISABLED: use %pylab or %matplotlib in the notebook to enable matplotlib.\n"
"        "
msgstr ""
"\n"
"        非推奨: matplotlib を有効にするにはノートブックで %pylab または %matplotlib\n"
"        を実行して下さい。"

#: notebook/notebookapp.py:988
msgid "Support for specifying --pylab on the command line has been removed."
msgstr "コマンドラインでの --pylab 指定はサポートされなくなりました。"

#: notebook/notebookapp.py:990
msgid "Please use `%pylab{0}` or `%matplotlib{0}` in the notebook itself."
msgstr "ノートブックの中で `%pylab{0}` または `%matplotlib{0}` を使ってください。"

#: notebook/notebookapp.py:995
msgid "The directory to use for notebooks and kernels."
msgstr "ノートブックとカーネルが使うディレクトリ。"

#: notebook/notebookapp.py:1018
#, python-format
msgid "No such notebook dir: '%r'"
msgstr "ノートブックディレクトリが見つかりません: '%r'"

#: notebook/notebookapp.py:1031
msgid "DEPRECATED use the nbserver_extensions dict instead"
msgstr "非推奨 nbserver_extensions 辞書を代わりに使用して下さい "

#: notebook/notebookapp.py:1036
msgid "server_extensions is deprecated, use nbserver_extensions"
msgstr "server_extensions が非推奨です。 nbserver_extensions を使用して下さい。"

#: notebook/notebookapp.py:1040
msgid "Dict of Python modules to load as notebook server extensions.Entry values can be used to enable and disable the loading ofthe extensions. The extensions will be loaded in alphabetical order."
msgstr "ノートブックサーバ拡張としてロードする Python モジュールの辞書。エントリー値を使用して拡張のロードを有効または無効にすることができます。 拡張子はアルファベット順にロードされます。"

#: notebook/notebookapp.py:1049
msgid "Reraise exceptions encountered loading server extensions?"
msgstr "サーバ拡張の読み込み中に例外が発生しましたか？"

#: notebook/notebookapp.py:1052
msgid ""
"(msgs/sec)\n"
"        Maximum rate at which messages can be sent on iopub before they are\n"
"        limited."
msgstr "メッセージが送信される前に iopub で送信可能な最大レート。"

#: notebook/notebookapp.py:1056
msgid ""
"(bytes/sec)\n"
"        Maximum rate at which stream output can be sent on iopub before they are\n"
"        limited."
msgstr ""
"(bytes/sec)\n"
"        ストリーム出力が送信制限される前に iopub で送信可能な最大レート。"

#: notebook/notebookapp.py:1060
msgid ""
"(sec) Time window used to \n"
"        check the message and data rate limits."
msgstr ""
"(sec) このウィンドウはメッセージとデータの帯域リミット\n"
"        をチェックする為に使用されます。"

#: notebook/notebookapp.py:1071
#, python-format
msgid "No such file or directory: %s"
msgstr "その様なファイルまたはディレクトリは存在しません: %s"

#: notebook/notebookapp.py:1141
msgid "Notebook servers are configured to only be run with a password."
msgstr "ノートブックサーバはパスワードが設定された場合にだけ動作するよう設定されています。"

#: notebook/notebookapp.py:1142
msgid "Hint: run the following command to set a password"
msgstr "ヒント: パスワードを設定するには以下のコマンドを実行します"

#: notebook/notebookapp.py:1143
msgid "\t$ python -m notebook.auth password"
msgstr ""

#: notebook/notebookapp.py:1181
#, python-format
msgid "The port %i is already in use, trying another port."
msgstr "ポート %i は既に使用されています。他のポートで試して下さい。"

#: notebook/notebookapp.py:1184
#, python-format
msgid "Permission to listen on port %i denied"
msgstr "ポート %i で待機する権限がありません"

#: notebook/notebookapp.py:1193
msgid "ERROR: the notebook server could not be started because no available port could be found."
msgstr "エラー: 有効なポートが見付からなかったためノートブックサーバを起動できませんでした。"

#: notebook/notebookapp.py:1199
msgid "[all ip addresses on your system]"
msgstr "[システム上の全ての IP アドレス]"

#: notebook/notebookapp.py:1223
#, python-format
msgid "Terminals not available (error was %s)"
msgstr "端末は存在しません (%s でエラー発生)"

#: notebook/notebookapp.py:1259
msgid "interrupted"
msgstr "中断しました"

#: notebook/notebookapp.py:1261
msgid "y"
msgstr ""

#: notebook/notebookapp.py:1262
msgid "n"
msgstr ""

#: notebook/notebookapp.py:1263
#, python-format
msgid "Shutdown this notebook server (%s/[%s])? "
msgstr "このノートブックサーバをシャットダウンしますか？ (%s/[%s])"

#: notebook/notebookapp.py:1269
msgid "Shutdown confirmed"
msgstr "シャットダウンの確認"

#: notebook/notebookapp.py:1273
msgid "No answer for 5s:"
msgstr "5秒間に応答がありません:"

#: notebook/notebookapp.py:1274
msgid "resuming operation..."
msgstr "操作を再開中..."

#: notebook/notebookapp.py:1282
#, python-format
msgid "received signal %s, stopping"
msgstr "シグナル %s を受信。停止します"

#: notebook/notebookapp.py:1338
#, python-format
msgid "Error loading server extension %s"
msgstr "サーバ拡張 %s の読み込みエラー"

#: notebook/notebookapp.py:1369
#, python-format
msgid "Shutting down %d kernels"
msgstr "%d 個のカーネルをシャットダウンしています"

#: notebook/notebookapp.py:1375
#, python-format
msgid "%d active kernel"
msgid_plural "%d active kernels"
msgstr[0] "%d 個のアクティブなカーネル"
msgstr[1] "%d 個のアクティブなカーネル"

#: notebook/notebookapp.py:1379
#, python-format
msgid ""
"The Jupyter Notebook is running at:\n"
"%s"
msgstr ""
"Jupyter Notebook は以下の URL 起動しています:\n"
"%s"

#: notebook/notebookapp.py:1426
msgid "Running as root is not recommended. Use --allow-root to bypass."
msgstr "root ユーザでの実行は推奨されません。バイパスするには --allow-root を使って下さい。"

#: notebook/notebookapp.py:1432
msgid "Use Control-C to stop this server and shut down all kernels (twice to skip confirmation)."
msgstr "サーバを停止し全てのカーネルをシャットダウンするには Control-C を使って下さい(確認をスキップするには2回)。"

#: notebook/notebookapp.py:1434
msgid "Welcome to Project Jupyter! Explore the various tools available and their corresponding documentation. If you are interested in contributing to the platform, please visit the communityresources section at http://jupyter.org/community.html."
msgstr "Project Jupyter へようこそ! 利用可能な色々なツールとそれに対応するドキュメントを探索して下さい。プラットフォームへの貢献に興味がある場合は http://jupyter.org/community.html の communityresources セクションにアクセスしてください。"

#: notebook/notebookapp.py:1445
#, python-format
msgid "No web browser found: %s."
msgstr "ウェブブラウザが見つかりません: %s"

#: notebook/notebookapp.py:1450
#, python-format
msgid "%s does not exist"
msgstr "%s は存在しません"

#: notebook/notebookapp.py:1484
msgid "Interrupted..."
msgstr "中断..."

#: notebook/services/contents/filemanager.py:506
#, python-format
msgid "Serving notebooks from local directory: %s"
msgstr "ローカルディレクトリからノートブックをサーブ: %s"

#: notebook/services/contents/manager.py:68
msgid "Untitled"
msgstr ""
