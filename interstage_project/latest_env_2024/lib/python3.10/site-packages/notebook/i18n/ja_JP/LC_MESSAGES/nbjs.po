# Translations template for Jupy<PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-06-27 14:04-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

#: notebook/static/base/js/dialog.js:161
msgid "Manually edit the JSON below to manipulate the metadata for this cell."
msgstr "このセルのメタデータを操作するには以下のJSONを手動で編集してください。"

#: notebook/static/base/js/dialog.js:163
msgid "Manually edit the JSON below to manipulate the metadata for this notebook."
msgstr "このノートブックのメタデータを操作するには以下のJSONを手動で編集してください。"

#: notebook/static/base/js/dialog.js:165
msgid " We recommend putting custom metadata attributes in an appropriately named substructure, so they don't conflict with those of others."
msgstr " カスタムメタデータ属性は適切な名前のサブ構造に含め他の属性と競合しないようにすることをお勧めします。"

#: notebook/static/base/js/dialog.js:180
msgid "Edit the metadata"
msgstr "メタデータの編集"

#: notebook/static/base/js/dialog.js:202
msgid "Edit Notebook Metadata"
msgstr "ノートブックのメタデータを編集"

#: notebook/static/base/js/dialog.js:204
msgid "Edit Cell Metadata"
msgstr "セルのメタデータを編集"

#: notebook/static/base/js/dialog.js:208
#: notebook/static/notebook/js/notebook.js:475
#: notebook/static/notebook/js/savewidget.js:71
#: notebook/static/tree/js/notebooklist.js:859
#: notebook/static/tree/js/notebooklist.js:1418
msgid "Cancel"
msgstr "キャンセル"

#: notebook/static/base/js/dialog.js:208
msgid "Edit"
msgstr "編集"

#: notebook/static/base/js/dialog.js:208
#: notebook/static/notebook/js/kernelselector.js:278
#: notebook/static/notebook/js/mathjaxutils.js:42
#: notebook/static/notebook/js/notebook.js:469
#: notebook/static/notebook/js/notificationarea.js:187
#: notebook/static/notebook/js/savewidget.js:71
#: notebook/static/tree/js/newnotebook.js:97
#: notebook/static/tree/js/notebooklist.js:859
msgid "OK"
msgstr "OK"

#: notebook/static/base/js/dialog.js:208
msgid "Apply"
msgstr "適用"

#: notebook/static/base/js/dialog.js:225
msgid "WARNING: Could not save invalid JSON."
msgstr "警告: 不正な JSON のため保存できませんでした。"

#: notebook/static/base/js/dialog.js:247
msgid "There are no attachments for this cell."
msgstr "このセルには添付ファイルはありません。"

#: notebook/static/base/js/dialog.js:250
msgid "Current cell attachments"
msgstr "現在のセルの添付ファイル"

#: notebook/static/base/js/dialog.js:259
#: notebook/static/notebook/js/celltoolbarpresets/attachments.js:46
msgid "Attachments"
msgstr "添付ファイル"

#: notebook/static/base/js/dialog.js:283
msgid "Restore"
msgstr "復元"

#: notebook/static/base/js/dialog.js:293
#: notebook/static/tree/js/notebooklist.js:1018
msgid "Delete"
msgstr "削除"

#: notebook/static/base/js/dialog.js:342 notebook/static/base/js/dialog.js:386
msgid "Edit attachments"
msgstr "添付ファイルを編集"

#: notebook/static/base/js/dialog.js:348
msgid "Edit Notebook Attachments"
msgstr "ノートブックの添付ファイルを編集"

#: notebook/static/base/js/dialog.js:350
msgid "Edit Cell Attachments"
msgstr "セルの添付ファイルを編集"

#: notebook/static/base/js/dialog.js:373
msgid "Select a file to insert."
msgstr "挿入するファイルを選択して下さい。"

#: notebook/static/base/js/dialog.js:399
msgid "Select a file"
msgstr "ファイルを選択"

#: notebook/static/notebook/js/about.js:14
msgid "You are using Jupyter notebook."
msgstr "Jupyter notebook を使用しています。"

#: notebook/static/notebook/js/about.js:16
msgid "The version of the notebook server is: "
msgstr "ノートブックサーバのバージョン"

#: notebook/static/notebook/js/about.js:22
msgid "The server is running on this version of Python:"
msgstr "サーバはこのバージョンの Python で稼働しています:"

#: notebook/static/notebook/js/about.js:25
msgid "Waiting for kernel to be available..."
msgstr "利用可能なカーネルを待機しています..."

#: notebook/static/notebook/js/about.js:27
msgid "Server Information:"
msgstr "サーバ情報:"

#: notebook/static/notebook/js/about.js:29
msgid "Current Kernel Information:"
msgstr "現在のカーネル情報:"

#: notebook/static/notebook/js/about.js:32
msgid "Could not access sys_info variable for version information."
msgstr "バージョン情報のための sys_info 変数にアクセスできませんでした。"

#: notebook/static/notebook/js/about.js:34
msgid "Cannot find sys_info!"
msgstr "sys_info が見つかりません!"

#: notebook/static/notebook/js/about.js:38
msgid "About Jupyter Notebook"
msgstr "Jupyter Notebook について"

#: notebook/static/notebook/js/about.js:47
msgid "unable to contact kernel"
msgstr "kernel と通信できません"

#: notebook/static/notebook/js/actions.js:69
msgid "toggle rtl layout"
msgstr "rtl レイアウトをトグル"

#: notebook/static/notebook/js/actions.js:70
msgid "Toggle the screen directionality between left-to-right and right-to-left"
msgstr "画面の表示方向を、左から右、と右から左、でトグル"

#: notebook/static/notebook/js/actions.js:76
msgid "edit command mode keyboard shortcuts"
msgstr "コマンドモードキーボードショートカットを編集"

#: notebook/static/notebook/js/actions.js:77
msgid "Open a dialog to edit the command mode keyboard shortcuts"
msgstr "コマンドモードのキーボードショートカットを編集するためにダイアログを開きます"

#: notebook/static/notebook/js/actions.js:97
msgid "restart kernel"
msgstr "カーネルを再起動"

#: notebook/static/notebook/js/actions.js:98
msgid "restart the kernel (no confirmation dialog)"
msgstr "カーネルを再起動 (確認ダイアログ無し)"

#: notebook/static/notebook/js/actions.js:106
msgid "confirm restart kernel"
msgstr "カーネルを再起動の確認"

#: notebook/static/notebook/js/actions.js:107
msgid "restart the kernel (with dialog)"
msgstr "カーネルを再起動 (確認ダイアログあり)"

#: notebook/static/notebook/js/actions.js:113
msgid "restart kernel and run all cells"
msgstr "カーネルを再起動し全てのセルを実行"

#: notebook/static/notebook/js/actions.js:114
msgid "restart the kernel, then re-run the whole notebook (no confirmation dialog)"
msgstr "カーネルを再起動しノートブック全体を再実行 (確認ダイアログ無し)"

#: notebook/static/notebook/js/actions.js:120
msgid "confirm restart kernel and run all cells"
msgstr "カーネルを再起動し全てのセルを実行する確認"

#: notebook/static/notebook/js/actions.js:121
msgid "restart the kernel, then re-run the whole notebook (with dialog)"
msgstr "カーネルを再起動しノートブック全体を再実行 (確認ダイアログあり)"

#: notebook/static/notebook/js/actions.js:127
msgid "restart kernel and clear output"
msgstr "カーネルを再起動し出力をクリア"

#: notebook/static/notebook/js/actions.js:128
msgid "restart the kernel and clear all output (no confirmation dialog)"
msgstr "カーネルを再起動し全ての出力をクリア (確認ダイアログ無し)"

#: notebook/static/notebook/js/actions.js:134
msgid "confirm restart kernel and clear output"
msgstr "カーネルを再起動し出力をクリアする確認"

#: notebook/static/notebook/js/actions.js:135
msgid "restart the kernel and clear all output (with dialog)"
msgstr "カーネルを再起動し出力をクリア (確認ダイアログあり)"

#: notebook/static/notebook/js/actions.js:142
#: notebook/static/notebook/js/actions.js:143
msgid "interrupt the kernel"
msgstr "カーネルを中断"

#: notebook/static/notebook/js/actions.js:150
msgid "run cell and select next"
msgstr "セルを実行し次を選択"

#: notebook/static/notebook/js/actions.js:152
msgid "run cell, select below"
msgstr "セルを実行し下を選択"

#: notebook/static/notebook/js/actions.js:159
#: notebook/static/notebook/js/actions.js:160
msgid "run selected cells"
msgstr "選択されているセルを実行"

#: notebook/static/notebook/js/actions.js:167
#: notebook/static/notebook/js/actions.js:168
msgid "run cell and insert below"
msgstr "セルを実行し下に挿入"

#: notebook/static/notebook/js/actions.js:175
#: notebook/static/notebook/js/actions.js:176
msgid "run all cells"
msgstr "全てのセルを実行"

#: notebook/static/notebook/js/actions.js:183
#: notebook/static/notebook/js/actions.js:184
msgid "run all cells above"
msgstr "ここまでのセルを実行"

#: notebook/static/notebook/js/actions.js:190
#: notebook/static/notebook/js/actions.js:191
msgid "run all cells below"
msgstr "以下の全てのセルを実行"

#: notebook/static/notebook/js/actions.js:197
#: notebook/static/notebook/js/actions.js:198
msgid "enter command mode"
msgstr "コマンドラインモードに移る"

#: notebook/static/notebook/js/actions.js:205
#: notebook/static/notebook/js/actions.js:206
msgid "insert image"
msgstr "画像の挿入"

#: notebook/static/notebook/js/actions.js:213
#: notebook/static/notebook/js/actions.js:214
msgid "cut cell attachments"
msgstr "セルの添付ファイルを切り取り"

#: notebook/static/notebook/js/actions.js:221
#: notebook/static/notebook/js/actions.js:222
msgid "copy cell attachments"
msgstr "セルの添付ファイルをコピー"

#: notebook/static/notebook/js/actions.js:229
#: notebook/static/notebook/js/actions.js:230
msgid "paste cell attachments"
msgstr "セルの添付ファイルをペースト"

#: notebook/static/notebook/js/actions.js:237
#: notebook/static/notebook/js/actions.js:238
msgid "split cell at cursor"
msgstr "カーソル位置でセルを分割"

#: notebook/static/notebook/js/actions.js:245
#: notebook/static/notebook/js/actions.js:246
msgid "enter edit mode"
msgstr "編集モードに移る"

#: notebook/static/notebook/js/actions.js:253
msgid "select previous cell"
msgstr "1つ前のセルを選択"

#: notebook/static/notebook/js/actions.js:254
msgid "select cell above"
msgstr "上のセルを選択"

#: notebook/static/notebook/js/actions.js:265
msgid "select next cell"
msgstr "次のセルを選択"

#: notebook/static/notebook/js/actions.js:266
msgid "select cell below"
msgstr "下のセルを選択"

#: notebook/static/notebook/js/actions.js:277
msgid "extend selection above"
msgstr "選択を上に拡大"

#: notebook/static/notebook/js/actions.js:278
msgid "extend selected cells above"
msgstr "セルの選択を上に拡大"

#: notebook/static/notebook/js/actions.js:289
msgid "extend selection below"
msgstr "選択を下に拡大"

#: notebook/static/notebook/js/actions.js:290
msgid "extend selected cells below"
msgstr "セルの選択を下に拡大"

#: notebook/static/notebook/js/actions.js:301
#: notebook/static/notebook/js/actions.js:302
msgid "cut selected cells"
msgstr "選択されているセルを切り取り"

#: notebook/static/notebook/js/actions.js:312
#: notebook/static/notebook/js/actions.js:313
msgid "copy selected cells"
msgstr "選択されているセルをコピー"

#: notebook/static/notebook/js/actions.js:327
#: notebook/static/notebook/js/actions.js:328
msgid "paste cells above"
msgstr "上にセルをペースト"

#: notebook/static/notebook/js/actions.js:335
#: notebook/static/notebook/js/actions.js:336
msgid "paste cells below"
msgstr "下にセルをペースト"

#: notebook/static/notebook/js/actions.js:344
#: notebook/static/notebook/js/actions.js:345
msgid "insert cell above"
msgstr "上にセルを追加"

#: notebook/static/notebook/js/actions.js:354
#: notebook/static/notebook/js/actions.js:355
msgid "insert cell below"
msgstr "下にセルを追加"

#: notebook/static/notebook/js/actions.js:365
#: notebook/static/notebook/js/actions.js:366
msgid "change cell to code"
msgstr "セルをコードに変更"

#: notebook/static/notebook/js/actions.js:373
#: notebook/static/notebook/js/actions.js:374
msgid "change cell to markdown"
msgstr "セルを markdown に変更"

#: notebook/static/notebook/js/actions.js:381
#: notebook/static/notebook/js/actions.js:382
msgid "change cell to raw"
msgstr "セルを raw に変更"

#: notebook/static/notebook/js/actions.js:389
#: notebook/static/notebook/js/actions.js:390
msgid "change cell to heading 1"
msgstr "セルを表題1に変更"

#: notebook/static/notebook/js/actions.js:397
#: notebook/static/notebook/js/actions.js:398
msgid "change cell to heading 2"
msgstr "セルを表題2に変更"

#: notebook/static/notebook/js/actions.js:405
#: notebook/static/notebook/js/actions.js:406
msgid "change cell to heading 3"
msgstr "セルを表題3に変更"

#: notebook/static/notebook/js/actions.js:413
#: notebook/static/notebook/js/actions.js:414
msgid "change cell to heading 4"
msgstr "セルを表題4に変更"

#: notebook/static/notebook/js/actions.js:421
#: notebook/static/notebook/js/actions.js:422
msgid "change cell to heading 5"
msgstr "セルを表題5に変更"

#: notebook/static/notebook/js/actions.js:429
#: notebook/static/notebook/js/actions.js:430
msgid "change cell to heading 6"
msgstr "セルを表題6に変更"

#: notebook/static/notebook/js/actions.js:437
msgid "toggle cell output"
msgstr "セルの出力をトグル"

#: notebook/static/notebook/js/actions.js:438
msgid "toggle output of selected cells"
msgstr "選択中のセルの出力をトグル"

#: notebook/static/notebook/js/actions.js:445
msgid "toggle cell scrolling"
msgstr "セルのスクロールをトグル"

#: notebook/static/notebook/js/actions.js:446
msgid "toggle output scrolling of selected cells"
msgstr "選択中のセルの出力スクロールをトグル"

#: notebook/static/notebook/js/actions.js:453
msgid "clear cell output"
msgstr "セルの出力をクリア"

#: notebook/static/notebook/js/actions.js:454
msgid "clear output of selected cells"
msgstr "選択しているセルの出力をクリア"

#: notebook/static/notebook/js/actions.js:460
msgid "move cells down"
msgstr "セルを下に移動"

#: notebook/static/notebook/js/actions.js:461
msgid "move selected cells down"
msgstr "選択しているセルを下に移動"

#: notebook/static/notebook/js/actions.js:469
msgid "move cells up"
msgstr "セルを上に移動"

#: notebook/static/notebook/js/actions.js:470
msgid "move selected cells up"
msgstr "選択しているセルを上に移動"

#: notebook/static/notebook/js/actions.js:478
#: notebook/static/notebook/js/actions.js:479
msgid "toggle line numbers"
msgstr "行番号をトグル"

#: notebook/static/notebook/js/actions.js:486
#: notebook/static/notebook/js/actions.js:487
msgid "show keyboard shortcuts"
msgstr "キーボードショートカットを表示"

#: notebook/static/notebook/js/actions.js:494
msgid "delete cells"
msgstr "セルを削除"

#: notebook/static/notebook/js/actions.js:495
msgid "delete selected cells"
msgstr "選択されているセルを削除"

#: notebook/static/notebook/js/actions.js:502
#: notebook/static/notebook/js/actions.js:503
msgid "undo cell deletion"
msgstr "セルの削除を取り消す"

#: notebook/static/notebook/js/actions.js:512
msgid "merge cell with previous cell"
msgstr "1つ前のアクティブセルとマージ"

#: notebook/static/notebook/js/actions.js:513
msgid "merge cell above"
msgstr "上のセルとマージ"

#: notebook/static/notebook/js/actions.js:519
msgid "merge cell with next cell"
msgstr "次のセルとマージ"

#: notebook/static/notebook/js/actions.js:520
msgid "merge cell below"
msgstr "下のセルとマージ"

#: notebook/static/notebook/js/actions.js:527
#: notebook/static/notebook/js/actions.js:528
msgid "merge selected cells"
msgstr "選択されているセルをマージ"

#: notebook/static/notebook/js/actions.js:535
msgid "merge cells"
msgstr "セルをマージ"

#: notebook/static/notebook/js/actions.js:536
msgid "merge selected cells, or current cell with cell below if only one cell is selected"
msgstr "選択中のセル、または選択されていない場合は現在のセル以降をマージする"

#: notebook/static/notebook/js/actions.js:549
msgid "show command pallette"
msgstr "コマンドパレットの表示"

#: notebook/static/notebook/js/actions.js:550
msgid "open the command palette"
msgstr "コマンドパレットを開く"

#: notebook/static/notebook/js/actions.js:557
msgid "toggle all line numbers"
msgstr "全ての行番号をトグル"

#: notebook/static/notebook/js/actions.js:558
msgid "toggles line numbers in all cells, and persist the setting"
msgstr "全てのセルの行番号をトグルし設定を保存"

#: notebook/static/notebook/js/actions.js:569
msgid "show all line numbers"
msgstr "全ての行番号を保存"

#: notebook/static/notebook/js/actions.js:570
msgid "show line numbers in all cells, and persist the setting"
msgstr "全てのセルで行番号を表示し設定を保存"

#: notebook/static/notebook/js/actions.js:579
msgid "hide all line numbers"
msgstr "全ての行番号を非表示"

#: notebook/static/notebook/js/actions.js:580
msgid "hide line numbers in all cells, and persist the setting"
msgstr "すべてのセルの行番号を非表示にし設定を保持します"

#: notebook/static/notebook/js/actions.js:589
msgid "toggle header"
msgstr "ヘッダをトグル"

#: notebook/static/notebook/js/actions.js:590
msgid "switch between showing and hiding the header"
msgstr "ヘッダの表示と非表示を切り替えます"

#: notebook/static/notebook/js/actions.js:605
#: notebook/static/notebook/js/actions.js:606
msgid "show the header"
msgstr "ヘッダを表示"

#: notebook/static/notebook/js/actions.js:615
#: notebook/static/notebook/js/actions.js:616
msgid "hide the header"
msgstr "ヘッダを非表示"

#: notebook/static/notebook/js/actions.js:646
msgid "toggle toolbar"
msgstr "ツールバーをトグル"

#: notebook/static/notebook/js/actions.js:647
msgid "switch between showing and hiding the toolbar"
msgstr "ツールバーの表示と非表示を切り替える"

#: notebook/static/notebook/js/actions.js:660
#: notebook/static/notebook/js/actions.js:661
msgid "show the toolbar"
msgstr "ツールバーを表示"

#: notebook/static/notebook/js/actions.js:669
#: notebook/static/notebook/js/actions.js:670
msgid "hide the toolbar"
msgstr "ツールバーを非表示"

#: notebook/static/notebook/js/actions.js:678
#: notebook/static/notebook/js/actions.js:679
msgid "close the pager"
msgstr "ページャを閉じる"

#: notebook/static/notebook/js/actions.js:704
msgid "ignore"
msgstr "無視"

#: notebook/static/notebook/js/actions.js:710
#: notebook/static/notebook/js/actions.js:711
msgid "move cursor up"
msgstr "カーソルを上に移動"

#: notebook/static/notebook/js/actions.js:731
#: notebook/static/notebook/js/actions.js:732
msgid "move cursor down"
msgstr "カーソルを下に移動"

#: notebook/static/notebook/js/actions.js:750
#: notebook/static/notebook/js/actions.js:751
msgid "scroll notebook down"
msgstr "ノートブックを下にスクロール"

#: notebook/static/notebook/js/actions.js:760
#: notebook/static/notebook/js/actions.js:761
msgid "scroll notebook up"
msgstr "ノートブックを上にスクロール"

#: notebook/static/notebook/js/actions.js:770
msgid "scroll cell center"
msgstr "セルを中央までスクロール"

#: notebook/static/notebook/js/actions.js:771
msgid "Scroll the current cell to the center"
msgstr "現在のセルが中央になる様にスクロール"

#: notebook/static/notebook/js/actions.js:781
msgid "scroll cell top"
msgstr "セルを最上部までスクロール"

#: notebook/static/notebook/js/actions.js:782
msgid "Scroll the current cell to the top"
msgstr "現在のセルが最上部になる様にスクロール"

#: notebook/static/notebook/js/actions.js:792
msgid "duplicate notebook"
msgstr "ノートブックの複製"

#: notebook/static/notebook/js/actions.js:793
msgid "Create and open a copy of the current notebook"
msgstr "現在のノートブックのコピーを作成し開く"

#: notebook/static/notebook/js/actions.js:799
msgid "trust notebook"
msgstr "ノートブックを信頼"

#: notebook/static/notebook/js/actions.js:800
msgid "Trust the current notebook"
msgstr "現在のノートブックを信頼する"

#: notebook/static/notebook/js/actions.js:806
msgid "rename notebook"
msgstr "ノートブックをリネーム"

#: notebook/static/notebook/js/actions.js:807
msgid "Rename the current notebook"
msgstr "現在のノートブックをリネームする"

#: notebook/static/notebook/js/actions.js:813
msgid "toggle all cells output collapsed"
msgstr ""

#: notebook/static/notebook/js/actions.js:814
msgid "Toggle the hidden state of all output areas"
msgstr ""

#: notebook/static/notebook/js/actions.js:820
msgid "toggle all cells output scrolled"
msgstr ""

#: notebook/static/notebook/js/actions.js:821
msgid "Toggle the scrolling state of all output areas"
msgstr ""

#: notebook/static/notebook/js/actions.js:828
msgid "clear all cells output"
msgstr "全てのセル出力をクリア"

#: notebook/static/notebook/js/actions.js:829
msgid "Clear the content of all the outputs"
msgstr "全ての出力コンテンツをクリア"

#: notebook/static/notebook/js/actions.js:835
msgid "save notebook"
msgstr "ノートブックの保存"

#: notebook/static/notebook/js/actions.js:836
msgid "Save and Checkpoint"
msgstr "保存とチェックポイント"

#: notebook/static/notebook/js/cell.js:79
msgid "Warning: accessing Cell.cm_config directly is deprecated."
msgstr "警告: Cell.cm_config に直接アクセスすることは非推奨です。"

#: notebook/static/notebook/js/cell.js:763
#, python-format
msgid "Unrecognized cell type: %s"
msgstr "認識できないセル種別: %s"

#: notebook/static/notebook/js/cell.js:777
msgid "Unrecognized cell type"
msgstr "認識できないセル種別"

#: notebook/static/notebook/js/celltoolbar.js:296
#, python-format
msgid "Error in cell toolbar callback %s"
msgstr "セルツールバーのコールバック %s でエラー"

#: notebook/static/notebook/js/clipboard.js:53
#, python-format
msgid "Clipboard types: %s"
msgstr "クリップボードの種別: %s"

#: notebook/static/notebook/js/clipboard.js:96
msgid "Dialog for paste from system clipboard"
msgstr "システムのクリップボードから張り付け"

#: notebook/static/notebook/js/clipboard.js:109
msgid "Ctrl-V"
msgstr ""

#: notebook/static/notebook/js/clipboard.js:111
msgid "Cmd-V"
msgstr ""

#: notebook/static/notebook/js/clipboard.js:113
#, python-format
msgid "Press %s again to paste"
msgstr "ペーストするには %s をもう一度押します"

#: notebook/static/notebook/js/clipboard.js:116
msgid "Why is this needed? "
msgstr "これが必要ですか？"

#: notebook/static/notebook/js/clipboard.js:118
msgid "We can't get paste events in this browser without a text box. "
msgstr "このブラウザではテキストボックスなしに貼り付けイベントを受け取ることができません。"

#: notebook/static/notebook/js/clipboard.js:119
msgid "There's an invisible text box focused in this dialog."
msgstr "このダイアログには目に見えないテキストボックスがあります。"

#: notebook/static/notebook/js/clipboard.js:125
#, python-format
msgid "%s to paste"
msgstr "ペーストするには %s"

#: notebook/static/notebook/js/codecell.js:310
msgid "Can't execute cell since kernel is not set."
msgstr "カーネルが存在しないのでセルを実行できません。"

#: notebook/static/notebook/js/codecell.js:472
msgid "In"
msgstr "入力"

#: notebook/static/notebook/js/kernelselector.js:269
#, python-format
msgid "Could not find a kernel matching %s. Please select a kernel:"
msgstr "%s にマッチするカーネルが見つかりませんでした。カーネルを選択して下さい:"

#: notebook/static/notebook/js/kernelselector.js:278
msgid "Continue Without Kernel"
msgstr "カーネル無しで続行"

#: notebook/static/notebook/js/kernelselector.js:278
msgid "Set Kernel"
msgstr "カーネルの設定"

#: notebook/static/notebook/js/kernelselector.js:281
msgid "Kernel not found"
msgstr "カーネルが見つかりません"

#: notebook/static/notebook/js/kernelselector.js:319
#: notebook/static/tree/js/newnotebook.js:99
msgid "Creating Notebook Failed"
msgstr "ノートブックの作成に失敗しました"

#: notebook/static/notebook/js/kernelselector.js:320
#: notebook/static/tree/js/notebooklist.js:1360
#, python-format
msgid "The error was: %s"
msgstr "エラー: %s"

#: notebook/static/notebook/js/maintoolbar.js:54
msgid "Run"
msgstr "実行"

#: notebook/static/notebook/js/maintoolbar.js:76
msgid "Code"
msgstr "コード"

#: notebook/static/notebook/js/maintoolbar.js:77
msgid "Markdown"
msgstr ""

#: notebook/static/notebook/js/maintoolbar.js:78
msgid "Raw NBConvert"
msgstr ""

#: notebook/static/notebook/js/maintoolbar.js:79
msgid "Heading"
msgstr "ヘッダ"

#: notebook/static/notebook/js/maintoolbar.js:115
msgid "unrecognized cell type:"
msgstr "不明なセル種別:"

#: notebook/static/notebook/js/mathjaxutils.js:45
#, python-format
msgid "Failed to retrieve MathJax from '%s'"
msgstr "%s から MathJax を取得できませんでした"

#: notebook/static/notebook/js/mathjaxutils.js:47
msgid "Math/LaTeX rendering will be disabled."
msgstr "Math/LaTeX レンダリングは無効になります。"

#: notebook/static/notebook/js/menubar.js:220
msgid "Trusted Notebook"
msgstr "信頼されたノートブック"

#: notebook/static/notebook/js/menubar.js:226
msgid "Trust Notebook"
msgstr "ノートブックを信頼する"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:16
#: notebook/static/notebook/js/menubar.js:383
msgid "None"
msgstr ""

#: notebook/static/notebook/js/menubar.js:406
msgid "No checkpoints"
msgstr "チェックポイントはありません"

#: notebook/static/notebook/js/menubar.js:465
msgid "Opens in a new window"
msgstr "新しいウィンドウで開く"

#: notebook/static/notebook/js/notebook.js:431
msgid "Autosave in progress, latest changes may be lost."
msgstr "自動保存が実行中です。最後の変更は失われるかもしれません。"

#: notebook/static/notebook/js/notebook.js:433
msgid "Unsaved changes will be lost."
msgstr "未保存の変更は破棄されます。"

#: notebook/static/notebook/js/notebook.js:438
msgid "The Kernel is busy, outputs may be lost."
msgstr "カーネルがビジーです。出力が欠けるかもしれません。"

#: notebook/static/notebook/js/notebook.js:461
msgid "This notebook is version %1$s, but we only fully support up to %2$s."
msgstr "このノートブックのバージョンは %1$s です。しかし完全なサポートは %2$s だけになります。"

#: notebook/static/notebook/js/notebook.js:463
msgid "You can still work with this notebook, but cell and output types introduced in later notebook versions will not be available."
msgstr "このノートブックはまだ動作しますがこれ以降のノートブックバージョンで導入されたセルおよび出力タイプは利用できなくなります。"

#: notebook/static/notebook/js/notebook.js:470
msgid "Restart and Run All Cells"
msgstr "再起動と全ての出力をクリア"

#: notebook/static/notebook/js/notebook.js:471
msgid "Restart and Clear All Outputs"
msgstr "再起動と全ての出力をクリア"

#: notebook/static/notebook/js/notebook.js:472
msgid "Restart"
msgstr "再起動"

#: notebook/static/notebook/js/notebook.js:473
msgid "Continue Running"
msgstr "実行を続行"

#: notebook/static/notebook/js/notebook.js:474
msgid "Reload"
msgstr "リロード"

#: notebook/static/notebook/js/notebook.js:476
msgid "Overwrite"
msgstr "上書き"

#: notebook/static/notebook/js/notebook.js:477
msgid "Trust"
msgstr "信頼"

#: notebook/static/notebook/js/notebook.js:478
msgid "Revert"
msgstr "復元"

#: notebook/static/notebook/js/notebook.js:483
msgid "Newer Notebook"
msgstr "新しいノートブック"

#: notebook/static/notebook/js/notebook.js:1548
msgid "Use markdown headings"
msgstr "Markdown のヘッダを使用"

#: notebook/static/notebook/js/notebook.js:1550
msgid "Jupyter no longer uses special heading cells. Instead, write your headings in Markdown cells using # characters:"
msgstr "Jupyter は特別な見出しセルを使用しなくなりました。代わりに # 文字を使用して Markdown セルに見出しを書きいて下さい:"

#: notebook/static/notebook/js/notebook.js:1553
msgid "## This is a level 2 heading"
msgstr "## これはレベル2のヘッダです"

#: notebook/static/notebook/js/notebook.js:2248
msgid "Restart kernel and re-run the whole notebook?"
msgstr "カーネルを再起動しノートブック全体を再実行する"

#: notebook/static/notebook/js/notebook.js:2250
msgid "Are you sure you want to restart the current kernel and re-execute the whole notebook?  All variables and outputs will be lost."
msgstr "現在のカーネルを再起動してノートブック全体を再実行しますか？全ての変数と出力は失われます。"

#: notebook/static/notebook/js/notebook.js:2275
msgid "Restart kernel and clear all output?"
msgstr "カーネルを再起動し全ての出力をクリアしますか？"

#: notebook/static/notebook/js/notebook.js:2277
msgid "Do you want to restart the current kernel and clear all output?  All variables and outputs will be lost."
msgstr "現在のカーネルを再起動してすべての出力をクリアしますか？全ての変数と出力は失われます。"

#: notebook/static/notebook/js/notebook.js:2322
msgid "Restart kernel?"
msgstr "カーネルを再起動しますか？"

#: notebook/static/notebook/js/notebook.js:2324
msgid "Do you want to restart the current kernel?  All variables will be lost."
msgstr "現在のカーネルを再起動しますか？全ての変数は失われます。"

#: notebook/static/notebook/js/notebook.js:2320
msgid "Shutdown kernel?"
msgstr "カーネルをシャットダウンしますか？"

#: notebook/static/notebook/js/notebook.js:2322
msgid "Do you want to shutdown the current kernel?  All variables will be lost."
msgstr "現在のカーネルをシャットダウンしますか？すべての変数は失われます。"

#: notebook/static/notebook/js/notebook.js:2734
msgid "Notebook changed"
msgstr "ノートブックは変更されました"

#: notebook/static/notebook/js/notebook.js:2735
msgid "The notebook file has changed on disk since the last time we opened or saved it. Do you want to overwrite the file on disk with the version open here, or load the version on disk (reload the page) ?"
msgstr ""

#: notebook/static/notebook/js/notebook.js:2782
#: notebook/static/notebook/js/notebook.js:2990
msgid "Notebook validation failed"
msgstr "ノートブックの検証に失敗しました"

#: notebook/static/notebook/js/notebook.js:2785
msgid "The save operation succeeded, but the notebook does not appear to be valid. The validation error was:"
msgstr "保存操作は成功しましたがノートブックは有効ではないようです。検証エラー:"

#: notebook/static/notebook/js/notebook.js:2836
msgid "A trusted Jupyter notebook may execute hidden malicious code when you open it. Selecting trust will immediately reload this notebook in a trusted state. For more information, see the Jupyter security documentation: "
msgstr "信頼された Jupyter ノートブックは開いた際に隠された悪意のあるコードが実行される可能性があります。信頼を選択すると直ちにリロードされこのノートブックは信頼できる状態になりします。詳細については、Jupyter のセキュリティドキュメントを参照してください。"

#: notebook/static/notebook/js/notebook.js:2840
msgid "here"
msgstr "ここ"

#: notebook/static/notebook/js/notebook.js:2848
msgid "Trust this notebook?"
msgstr "このノートブックを信頼しますか？"

#: notebook/static/notebook/js/notebook.js:2981
msgid "Notebook failed to load"
msgstr "ノートブックの読み込みに失敗しました"

#: notebook/static/notebook/js/notebook.js:2983
msgid "The error was: "
msgstr "エラー: "

#: notebook/static/notebook/js/notebook.js:2987
msgid "See the error console for details."
msgstr "詳しくはエラーコンソールを参照して下さい。"

#: notebook/static/notebook/js/notebook.js:2995
msgid "The notebook also failed validation:"
msgstr "ノートブックの検証に失敗しました:"

#: notebook/static/notebook/js/notebook.js:2997
msgid "An invalid notebook may not function properly. The validation error was:"
msgstr "無効なノートブックは正しく機能しない可能性があります。検証エラーは次の通りです:"

#: notebook/static/notebook/js/notebook.js:3036
#, python-format
msgid "This notebook has been converted from an older notebook format to the current notebook format v(%s)."
msgstr "このノートブックは古いノートブックの形式から現在の形式 v(%s) に変換されました。"

#: notebook/static/notebook/js/notebook.js:3038
#, python-format
msgid "This notebook has been converted from a newer notebook format to the current notebook format v(%s)."
msgstr "このノートブックは新しいノートブックの形式から現在の形式 v(%s) に変換されました。"

#: notebook/static/notebook/js/notebook.js:3046
msgid "The next time you save this notebook, the current notebook format will be used."
msgstr "次回このノートブックを保存する時は現在のノートブックの形式が使用されます。"

#: notebook/static/notebook/js/notebook.js:3051
msgid "Older versions of Jupyter may not be able to read the new format."
msgstr "古いバージョンの Jupyter は新しい形式を読むことができない場合があります。"

#: notebook/static/notebook/js/notebook.js:3053
msgid "Some features of the original notebook may not be available."
msgstr "オリジナルのノートブックの中には利用できない機能が幾つかあります。"

#: notebook/static/notebook/js/notebook.js:3056
msgid "To preserve the original version, close the notebook without saving it."
msgstr "元のバージョンを残すには保存せずにノートブックを閉じます"

#: notebook/static/notebook/js/notebook.js:3061
msgid "Notebook converted"
msgstr "ノートブックは変換されました"

#: notebook/static/notebook/js/notebook.js:3083
msgid "(No name)"
msgstr "(無題)"

#: notebook/static/notebook/js/notebook.js:3131
#, python-format
msgid "An unknown error occurred while loading this notebook. This version can load notebook formats %s or earlier. See the server log for details."
msgstr "このノートブックの読み込み中に不明なエラーが発生しました。このバージョンはノートブックの形式 %s とそれ以前のバージョンをロードできます。 詳細についてはサーバのログを参照して下さい。"

#: notebook/static/notebook/js/notebook.js:3142
msgid "Error loading notebook"
msgstr "ノートブックの読み込み中にエラー"

#: notebook/static/notebook/js/notebook.js:3243
msgid "Are you sure you want to revert the notebook to the latest checkpoint?"
msgstr "本当に最終チェックポイントへノートブックを復元しますか？"

#: notebook/static/notebook/js/notebook.js:3246
msgid "This cannot be undone."
msgstr "この操作は取り消せません。"

#: notebook/static/notebook/js/notebook.js:3249
msgid "The checkpoint was last updated at:"
msgstr "チェックポイントはこの時間に変更されました:"

#: notebook/static/notebook/js/notebook.js:3260
msgid "Revert notebook to checkpoint"
msgstr "ノートブックをチェックポイントへ復元"

#: notebook/static/notebook/js/notificationarea.js:77
#: notebook/static/notebook/js/tour.js:61
#: notebook/static/notebook/js/tour.js:67
msgid "Edit Mode"
msgstr "編集モード"

#: notebook/static/notebook/js/notificationarea.js:84
#: notebook/static/notebook/js/notificationarea.js:88
#: notebook/static/notebook/js/tour.js:54
msgid "Command Mode"
msgstr "コマンドモード"

#: notebook/static/notebook/js/notificationarea.js:95
msgid "Kernel Created"
msgstr "カーネルが作成されました"

#: notebook/static/notebook/js/notificationarea.js:99
msgid "Connecting to kernel"
msgstr "カーネルに接続中"

#: notebook/static/notebook/js/notificationarea.js:103
msgid "Not Connected"
msgstr "接続されていません"

#: notebook/static/notebook/js/notificationarea.js:106
msgid "click to reconnect"
msgstr "クリックして再接続"

#: notebook/static/notebook/js/notificationarea.js:115
msgid "Restarting kernel"
msgstr "カーネルを再起動中"

#: notebook/static/notebook/js/notificationarea.js:129
msgid "Kernel Restarting"
msgstr "カーネルは再起動中です"

#: notebook/static/notebook/js/notificationarea.js:130
msgid "The kernel appears to have died. It will restart automatically."
msgstr "カーネルが異常終了した様です。自動的に再起動します。"

#: notebook/static/notebook/js/notificationarea.js:140
#: notebook/static/notebook/js/notificationarea.js:198
#: notebook/static/notebook/js/notificationarea.js:218
msgid "Dead kernel"
msgstr "カーネルが異常終了!"

#: notebook/static/notebook/js/notificationarea.js:141
#: notebook/static/notebook/js/notificationarea.js:219
#: notebook/static/notebook/js/notificationarea.js:266
msgid "Kernel Dead"
msgstr "カーネルが異常終了"

#: notebook/static/notebook/js/notificationarea.js:145
msgid "Interrupting kernel"
msgstr "カーネルを中断します!"

#: notebook/static/notebook/js/notificationarea.js:151
msgid "No Connection to Kernel"
msgstr "カーネルへの接続がありません"

#: notebook/static/notebook/js/notificationarea.js:161
msgid "A connection to the notebook server could not be established. The notebook will continue trying to reconnect. Check your network connection or notebook server configuration."
msgstr "ノートブックサーバへの接続を確立できませんでした。ノートブックは再接続を試みます。ネットワーク接続またはノートブックサーバの設定を確認してください。"

#: notebook/static/notebook/js/notificationarea.js:166
msgid "Connection failed"
msgstr "接続に失敗しました"

#: notebook/static/notebook/js/notificationarea.js:179
msgid "No kernel"
msgstr "カーネルが見つかりません"

#: notebook/static/notebook/js/notificationarea.js:180
msgid "Kernel is not running"
msgstr "カーネルは起動していません"

#: notebook/static/notebook/js/notificationarea.js:187
msgid "Don't Restart"
msgstr "再起動しない"

#: notebook/static/notebook/js/notificationarea.js:187
msgid "Try Restarting Now"
msgstr "再起動を試みます"

#: notebook/static/notebook/js/notificationarea.js:191
msgid "The kernel has died, and the automatic restart has failed. It is possible the kernel cannot be restarted. If you are not able to restart the kernel, you will still be able to save the notebook, but running code will no longer work until the notebook is reopened."
msgstr "カーネルが異常終了し自動再起動が失敗しました。カーネルを再起動できない可能性があります。カーネルを再起動できない場合でもノートブックを保存することはできますがコードの実行はノートブックを開き直すまで機能しなくなります。"

#: notebook/static/notebook/js/notificationarea.js:225
msgid "No Kernel"
msgstr "カーネルが見つかりません"

#: notebook/static/notebook/js/notificationarea.js:252
msgid "Failed to start the kernel"
msgstr "カーネルの起動に失敗しました"

#: notebook/static/notebook/js/notificationarea.js:272
#: notebook/static/notebook/js/notificationarea.js:292
#: notebook/static/notebook/js/notificationarea.js:306
msgid "Kernel Busy"
msgstr "カーネルがビジー"

#: notebook/static/notebook/js/notificationarea.js:273
msgid "Kernel starting, please wait..."
msgstr "カーネルを起動しています。お待ちください..."

#: notebook/static/notebook/js/notificationarea.js:279
#: notebook/static/notebook/js/notificationarea.js:286
msgid "Kernel Idle"
msgstr "カーネルは休止状態"

#: notebook/static/notebook/js/notificationarea.js:280
msgid "Kernel ready"
msgstr "カーネルの準備が完了"

#: notebook/static/notebook/js/notificationarea.js:297
msgid "Using kernel: "
msgstr "カーネルを使用しています: "

#: notebook/static/notebook/js/notificationarea.js:298
msgid "Only candidate for language: %1$s was %2$s."
msgstr "唯一の言語の候補: %1$s は %2$s."

#: notebook/static/notebook/js/notificationarea.js:319
msgid "Loading notebook"
msgstr "ノートブックを読み込んでいます"

#: notebook/static/notebook/js/notificationarea.js:322
msgid "Notebook loaded"
msgstr "ノートブックが読み込まれました"

#: notebook/static/notebook/js/notificationarea.js:325
msgid "Saving notebook"
msgstr "ノートブックを保存しています"

#: notebook/static/notebook/js/notificationarea.js:328
msgid "Notebook saved"
msgstr "ノートブックが保存されました"

#: notebook/static/notebook/js/notificationarea.js:331
msgid "Notebook save failed"
msgstr "ノートブックの保存に失敗しました"

#: notebook/static/notebook/js/notificationarea.js:334
msgid "Notebook copy failed"
msgstr "ノートブックのコピーに失敗しました"

#: notebook/static/notebook/js/notificationarea.js:339
msgid "Checkpoint created"
msgstr "チェックポイントが作成されました"

#: notebook/static/notebook/js/notificationarea.js:347
msgid "Checkpoint failed"
msgstr "チェックポイントの失敗"

#: notebook/static/notebook/js/notificationarea.js:350
msgid "Checkpoint deleted"
msgstr "チェックポイントの削除"

#: notebook/static/notebook/js/notificationarea.js:353
msgid "Checkpoint delete failed"
msgstr "チェックポイントの削除に失敗しました"

#: notebook/static/notebook/js/notificationarea.js:356
msgid "Restoring to checkpoint..."
msgstr "チェックポイントの復元..."

#: notebook/static/notebook/js/notificationarea.js:359
msgid "Checkpoint restore failed"
msgstr "チェックポイントの復元に失敗しました"

#: notebook/static/notebook/js/notificationarea.js:364
msgid "Autosave disabled"
msgstr "自動保存は無効"

#: notebook/static/notebook/js/notificationarea.js:367
#, python-format
msgid "Saving every %d sec."
msgstr "%d 秒で保存"

#: notebook/static/notebook/js/notificationarea.js:383
msgid "Trusted"
msgstr "信頼済み"

#: notebook/static/notebook/js/notificationarea.js:385
msgid "Not Trusted"
msgstr "信頼されていません"

#: notebook/static/notebook/js/outputarea.js:75
msgid "click to expand output"
msgstr "出力を広げるにはクリックします"

#: notebook/static/notebook/js/outputarea.js:79
msgid "click to expand output; double click to hide output"
msgstr "出力を広げるにはクリックします; ダブルクリックで非表示"

#: notebook/static/notebook/js/outputarea.js:167
msgid "click to unscroll output; double click to hide"
msgstr "出力のスクロールを止めるためにクリック; ダブルクリックで非表示"

#: notebook/static/notebook/js/outputarea.js:174
msgid "click to scroll output; double click to hide"
msgstr "出力をスクロールするためにクリック; ダブルクリックで非表示"

#: notebook/static/notebook/js/outputarea.js:422
msgid "Javascript error adding output!"
msgstr "Javascript エラーが出力に追加されました!"

#: notebook/static/notebook/js/outputarea.js:427
msgid "See your browser Javascript console for more details."
msgstr "より詳細を知るにはブラウザの Javascript コンソールを確認して下さい。"

#: notebook/static/notebook/js/outputarea.js:468
#, python-format
msgid "Out[%s]:"
msgstr "出力[%s]:"

#: notebook/static/notebook/js/outputarea.js:577
#, python-format
msgid "Unrecognized output: %s"
msgstr "認識できない出力: %s"

#: notebook/static/notebook/js/pager.js:36
msgid "Open the pager in an external window"
msgstr "外部ウィンドウでページャを開く"

#: notebook/static/notebook/js/pager.js:45
msgid "Close the pager"
msgstr "ページャを閉じる"

#: notebook/static/notebook/js/pager.js:148
msgid "Jupyter Pager"
msgstr "Jupyter ページャ"

#: notebook/static/notebook/js/quickhelp.js:39
#: notebook/static/notebook/js/quickhelp.js:49
#: notebook/static/notebook/js/quickhelp.js:50
msgid "go to cell start"
msgstr "セルの先頭に移動"

#: notebook/static/notebook/js/quickhelp.js:40
#: notebook/static/notebook/js/quickhelp.js:51
#: notebook/static/notebook/js/quickhelp.js:52
msgid "go to cell end"
msgstr "セルの末尾に移動"

#: notebook/static/notebook/js/quickhelp.js:41
#: notebook/static/notebook/js/quickhelp.js:53
msgid "go one word left"
msgstr "単語1つ左に移動"

#: notebook/static/notebook/js/quickhelp.js:42
#: notebook/static/notebook/js/quickhelp.js:54
msgid "go one word right"
msgstr "単語1つ右に移動"

#: notebook/static/notebook/js/quickhelp.js:43
#: notebook/static/notebook/js/quickhelp.js:55
msgid "delete word before"
msgstr "前の単語を削除"

#: notebook/static/notebook/js/quickhelp.js:44
#: notebook/static/notebook/js/quickhelp.js:56
msgid "delete word after"
msgstr "続く単語を削除"

#: notebook/static/notebook/js/quickhelp.js:61
msgid "code completion or indent"
msgstr "コード補完またはインデント"

#: notebook/static/notebook/js/quickhelp.js:62
msgid "tooltip"
msgstr "ツールチップ"

#: notebook/static/notebook/js/quickhelp.js:63
msgid "indent"
msgstr "インデント"

#: notebook/static/notebook/js/quickhelp.js:64
msgid "dedent"
msgstr "インデント解除"

#: notebook/static/notebook/js/quickhelp.js:65
msgid "select all"
msgstr "全てを選択"

#: notebook/static/notebook/js/quickhelp.js:66
msgid "undo"
msgstr "元に戻す"

#: notebook/static/notebook/js/quickhelp.js:67
#: notebook/static/notebook/js/quickhelp.js:68
msgid "redo"
msgstr "やり直し"

#: notebook/static/notebook/js/quickhelp.js:102
#: notebook/static/notebook/js/quickhelp.js:243
msgid "Shift"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:103
msgid "Alt"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:104
msgid "Up"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:105
msgid "Down"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:106
msgid "Left"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:107
msgid "Right"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:108
#: notebook/static/notebook/js/quickhelp.js:246
msgid "Tab"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:109
msgid "Caps Lock"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:110
#: notebook/static/notebook/js/quickhelp.js:269
msgid "Esc"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:111
msgid "Ctrl"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:112
#: notebook/static/notebook/js/quickhelp.js:290
msgid "Enter"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:113
msgid "Page Up"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:114
#: notebook/static/notebook/js/quickhelp.js:130
msgid "Page Down"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:115
msgid "Home"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:116
msgid "End"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:117
#: notebook/static/notebook/js/quickhelp.js:245
msgid "Space"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:118
msgid "Backspace"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:119
msgid "Minus"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:130
msgid "PageUp"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:197
msgid "The Jupyter Notebook has two different keyboard input modes."
msgstr "Jupyter Notebook は異なる2つのキーボード入力モードを持っています。"

#: notebook/static/notebook/js/quickhelp.js:199
msgid "<b>Edit mode</b> allows you to type code or text into a cell and is indicated by a green cell border."
msgstr "<b>編集モード</b> は緑色のセル枠で表示され、セルにコードまたはテキストを入力できます。"

#: notebook/static/notebook/js/quickhelp.js:201
msgid "<b>Command mode</b> binds the keyboard to notebook level commands and is indicated by a grey cell border with a blue left margin."
msgstr "<b>コマンドモード</b> は青い左マージンを持つ灰色のセル枠で表示され、ノートブックレベルのコマンドにキーボードをバインドします。"

#: notebook/static/notebook/js/quickhelp.js:222
#: notebook/static/notebook/js/tooltip.js:58
#: notebook/static/notebook/js/tooltip.js:69
msgid "Close"
msgstr "閉じる"

#: notebook/static/notebook/js/quickhelp.js:225
msgid "Keyboard shortcuts"
msgstr "キーボードショートカット"

#: notebook/static/notebook/js/quickhelp.js:240
msgid "Command"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:241
msgid "Control"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:242
msgid "Option"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:244
msgid "Return"
msgstr ""

#: notebook/static/notebook/js/quickhelp.js:270
#, python-format
msgid "Command Mode (press %s to enable)"
msgstr "コマンドモード (有効にするには %s を押下)"

#: notebook/static/notebook/js/quickhelp.js:272
msgid "Edit Shortcuts"
msgstr "ショートカットを編集"

#: notebook/static/notebook/js/quickhelp.js:275
msgid "edit command-mode keyboard shortcuts"
msgstr "コマンドモードのキーボードショートカットを編集"

#: notebook/static/notebook/js/quickhelp.js:292
#, python-format
msgid "Edit Mode (press %s to enable)"
msgstr "編集モード (有効にするには %s を押下)"

#: notebook/static/notebook/js/savewidget.js:49
msgid "Autosave Failed!"
msgstr "自動保存に失敗!"

#: notebook/static/notebook/js/savewidget.js:71
#: notebook/static/tree/js/notebooklist.js:846
#: notebook/static/tree/js/notebooklist.js:859
msgid "Rename"
msgstr "リネーム"

#: notebook/static/notebook/js/savewidget.js:78
#: notebook/static/tree/js/notebooklist.js:837
msgid "Enter a new notebook name:"
msgstr "新しいノートブックの名前を入力:"

#: notebook/static/notebook/js/savewidget.js:86
msgid "Rename Notebook"
msgstr "ノートブックのリネーム"

#: notebook/static/notebook/js/savewidget.js:98
msgid "Invalid notebook name. Notebook names must have 1 or more characters and can contain any characters except :/\\. Please enter a new notebook name:"
msgstr "不正なノートブックの名前です。ノートブックの名前は :/\\ を除く1文字以上でなければなりません。新しいノートブックの名前を入力して下さい:"

#: notebook/static/notebook/js/savewidget.js:103
msgid "Renaming..."
msgstr "リネーム中..."

#: notebook/static/notebook/js/savewidget.js:109
msgid "Unknown error"
msgstr "不明なエラー"

#: notebook/static/notebook/js/savewidget.js:178
msgid "no checkpoint"
msgstr "チェックポイントはありません"

#: notebook/static/notebook/js/savewidget.js:193
#, python-format
msgid "Last Checkpoint: %s"
msgstr "最終チェックポイント: %s"

#: notebook/static/notebook/js/savewidget.js:217
msgid "(unsaved changes)"
msgstr "(未保存の変更)"

#: notebook/static/notebook/js/savewidget.js:219
msgid "(autosaved)"
msgstr "(自動保存)"

#: notebook/static/notebook/js/searchandreplace.js:74
#, python-format
msgid "Warning: too many matches (%d). Some changes might not be shown or applied."
msgstr "警告: マッチが多すぎます (%d 個)。幾らかの変更は表示または適用されません。"

#: notebook/static/notebook/js/searchandreplace.js:77
#, python-format
msgid "%d match"
msgid_plural "%d matches"
msgstr[0] "%d 個にマッチ"
msgstr[1] "%d 個にマッチ"

#: notebook/static/notebook/js/searchandreplace.js:145
msgid "More than 100 matches, aborting"
msgstr "100 個以上にマッチしたため中断します"

#: notebook/static/notebook/js/searchandreplace.js:166
msgid "Use regex (JavaScript regex syntax)"
msgstr "正規表現の使用 (JavaScript 正規表現)"

#: notebook/static/notebook/js/searchandreplace.js:174
msgid "Replace in selected cells"
msgstr "選択中のセルを入れ替え"

#: notebook/static/notebook/js/searchandreplace.js:181
msgid "Match case"
msgstr "大文字と小文字の両方にマッチ"

#: notebook/static/notebook/js/searchandreplace.js:187
msgid "Find"
msgstr "検索"

#: notebook/static/notebook/js/searchandreplace.js:203
msgid "Replace"
msgstr "置換"

#: notebook/static/notebook/js/searchandreplace.js:255
msgid "No matches, invalid or empty regular expression"
msgstr "マッチしません。不正または空の正規表現です"

#: notebook/static/notebook/js/searchandreplace.js:370
msgid "Replace All"
msgstr "全て置換"

#: notebook/static/notebook/js/searchandreplace.js:374
msgid "Find and Replace"
msgstr "検索と置換"

#: notebook/static/notebook/js/searchandreplace.js:400
#: notebook/static/notebook/js/searchandreplace.js:401
msgid "find and replace"
msgstr "検索と置換"

#: notebook/static/notebook/js/textcell.js:551
msgid "Write raw LaTeX or other formats here, for use with nbconvert. It will not be rendered in the notebook. When passing through nbconvert, a Raw Cell's content is added to the output unmodified."
msgstr "nbconvert で使うために生の LaTeX や他の形式をここに書いてください。これらはノートブックには表示されません。nbconvert に渡されると Raw Cell の内容が変更されずに出力に追加されます。"

#: notebook/static/notebook/js/tooltip.js:41
msgid "Grow the tooltip vertically (press shift-tab twice)"
msgstr "ツールチップを垂直方向に広げる（shift-tabを2回押す）"

#: notebook/static/notebook/js/tooltip.js:48
msgid "show the current docstring in pager (press shift-tab 4 times)"
msgstr "現在の docstring をページャで表示します（shift-tabを4回押します）"

#: notebook/static/notebook/js/tooltip.js:49
msgid "Open in Pager"
msgstr "ページャで開く"

#: notebook/static/notebook/js/tooltip.js:68
msgid "Tooltip will linger for 10 seconds while you type"
msgstr "ツールチップは入力中の 10 秒間表示されます"

#: notebook/static/notebook/js/tour.js:27
msgid "Welcome to the Notebook Tour"
msgstr "ノートブックツアーへようこそ"

#: notebook/static/notebook/js/tour.js:30
msgid "You can use the left and right arrow keys to go backwards and forwards."
msgstr "先に進んだり前に戻るにはカーソルの左と右を使う事ができます。"

#: notebook/static/notebook/js/tour.js:33
msgid "Filename"
msgstr "ファイル名"

#: notebook/static/notebook/js/tour.js:35
msgid "Click here to change the filename for this notebook."
msgstr "このノートブックのファイル名を変更するにはここをクリックします。"

#: notebook/static/notebook/js/tour.js:39
msgid "Notebook Menubar"
msgstr "ノートブックメニューバー"

#: notebook/static/notebook/js/tour.js:40
msgid "The menubar has menus for actions on the notebook, its cells, and the kernel it communicates with."
msgstr "メニューバーにはノートブック、そのセル、およびそれらが通信するカーネルに対するアクションのためのメニューがあります。"

#: notebook/static/notebook/js/tour.js:44
msgid "Notebook Toolbar"
msgstr "ノートブックツールバー"

#: notebook/static/notebook/js/tour.js:45
msgid "The toolbar has buttons for the most common actions. Hover your mouse over each button for more information."
msgstr "ツールバーには最も一般的な操作のボタンが置かれます。 詳細については各ボタンの上にマウスを移動してください。"

#: notebook/static/notebook/js/tour.js:48
msgid "Mode Indicator"
msgstr "モードインジケータ"

#: notebook/static/notebook/js/tour.js:50
msgid "The Notebook has two modes: Edit Mode and Command Mode. In this area, an indicator can appear to tell you which mode you are in."
msgstr "ノートブックには編集モードとコマンドモードの2つのモードがあります。この領域にはどのモードにいるのかを示すインジケータが表示されます。"

#: notebook/static/notebook/js/tour.js:58
msgid "Right now you are in Command Mode, and many keyboard shortcuts are available. In this mode, no icon is displayed in the indicator area."
msgstr "現在あなたはコマンドモードにいるので多くのキーボードショートカットが利用可能です。 このモードではインジケータ領域にアイコンは表示されません。"

#: notebook/static/notebook/js/tour.js:64
msgid "Pressing <code>Enter</code> or clicking in the input text area of the cell switches to Edit Mode."
msgstr "<code>Enter</code>を押下、セルのテキストの入力エリアをクリックすると編集モードに切り替わります。"

#: notebook/static/notebook/js/tour.js:70
msgid "Notice that the border around the currently active cell changed color. Typing will insert text into the currently active cell."
msgstr "現在アクティブなセルの周囲の境界線の色が変わることに注目してください。文字を入力すると現在アクティブなセルにテキストが挿入されます。"

#: notebook/static/notebook/js/tour.js:73
msgid "Back to Command Mode"
msgstr "コマンドモードに戻る"

#: notebook/static/notebook/js/tour.js:76
msgid "Pressing <code>Esc</code> or clicking outside of the input text area takes you back to Command Mode."
msgstr "<code>Esc</code>を押下、またはテキストの入力エリアの外側をクリックするとコマンドモードに戻ります。"

#: notebook/static/notebook/js/tour.js:79
msgid "Keyboard Shortcuts"
msgstr "キーボードショートカット"

#: notebook/static/notebook/js/tour.js:91
msgid "You can click here to get a list of all of the keyboard shortcuts."
msgstr "ここをクリックすると全てのキーボードショートカットの一覧が表示されます。"

#: notebook/static/notebook/js/tour.js:94
#: notebook/static/notebook/js/tour.js:100
msgid "Kernel Indicator"
msgstr "カーネルインジケータ"

#: notebook/static/notebook/js/tour.js:97
msgid "This is the Kernel indicator. It looks like this when the Kernel is idle."
msgstr "これはカーネルインジケータです。カーネルがアイドル状態のときはこの様に表示されます。"

#: notebook/static/notebook/js/tour.js:103
msgid "The Kernel indicator looks like this when the Kernel is busy."
msgstr "カーネルインジケータはカーネルがビジーのときはこのように表示されます。"

#: notebook/static/notebook/js/tour.js:107
msgid "Interrupting the Kernel"
msgstr "カーネルの中断"

#: notebook/static/notebook/js/tour.js:109
msgid "To cancel a computation in progress, you can click here."
msgstr "実行中の計算をキャンセルするにはここをクリックして下さい。"

#: notebook/static/notebook/js/tour.js:114
msgid "Notification Area"
msgstr "通知エリア"

#: notebook/static/notebook/js/tour.js:115
msgid "Messages in response to user actions (Save, Interrupt, etc.) appear here."
msgstr "ユーザーアクション (保存、割り込みなど) に応じたメッセージがここに表示されます。"

#: notebook/static/notebook/js/tour.js:117
msgid "End of Tour"
msgstr "ツアーの終わり"

#: notebook/static/notebook/js/tour.js:120
msgid "This concludes the Jupyter Notebook User Interface Tour."
msgstr "これで Jupyter Notebook のユーザーインタフェースのツアーは終了です。"

#: notebook/static/notebook/js/celltoolbarpresets/attachments.js:32
msgid "Edit Attachments"
msgstr "添付ファイルを編集"

#: notebook/static/notebook/js/celltoolbarpresets/default.js:19
msgid "Cell"
msgstr "セル"

#: notebook/static/notebook/js/celltoolbarpresets/default.js:29
#: notebook/static/notebook/js/celltoolbarpresets/default.js:47
msgid "Edit Metadata"
msgstr "メタデータを編集"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:22
msgid "Custom"
msgstr "カスタム"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:32
msgid "Set the MIME type of the raw cell:"
msgstr "Raw セルの MIME タイプを設定:"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:40
msgid "Raw Cell MIME Type"
msgstr "Raw セル MIME タイプ"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:74
msgid "Raw NBConvert Format"
msgstr "Raw NBConvert 書式"

#: notebook/static/notebook/js/celltoolbarpresets/rawcell.js:81
msgid "Raw Cell Format"
msgstr "Raw セル書式"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:15
msgid "Slide"
msgstr "スライド"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:16
msgid "Sub-Slide"
msgstr "サブスライド"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:17
msgid "Fragment"
msgstr "フラグメント"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:18
msgid "Skip"
msgstr "スキップ"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:19
msgid "Notes"
msgstr "ノート"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:35
msgid "Slide Type"
msgstr "スライドタイプ"

#: notebook/static/notebook/js/celltoolbarpresets/slideshow.js:41
msgid "Slideshow"
msgstr "スライドショー"

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:133
msgid "Add tag"
msgstr "タグを追加"

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:163
msgid "Edit the list of tags below. All whitespace is treated as tag separators."
msgstr "以下のタグ一覧を編集。全ての空白はタグのセパレータとして扱われます。"

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:172
msgid "Edit the tags"
msgstr "タグを編集"

#: notebook/static/notebook/js/celltoolbarpresets/tags.js:186
msgid "Edit Tags"
msgstr "タグを編集"

#: notebook/static/tree/js/kernellist.js:86
#: notebook/static/tree/js/terminallist.js:105
msgid "Shutdown"
msgstr "シャットダウン"

#: notebook/static/tree/js/newnotebook.js:70
#, python-format
msgid "Create a new notebook with %s"
msgstr "新しいノートブック %s を作成"

#: notebook/static/tree/js/newnotebook.js:101
msgid "An error occurred while creating a new notebook."
msgstr "新しいノートブックの作成中にエラーが発生しました。"

#: notebook/static/tree/js/notebooklist.js:122
msgid "Creating File Failed"
msgstr "ファイルの作成に失敗"

#: notebook/static/tree/js/notebooklist.js:124
msgid "An error occurred while creating a new file."
msgstr "新しいファイルの作成中に失敗しました。"

#: notebook/static/tree/js/notebooklist.js:142
msgid "Creating Folder Failed"
msgstr "フォルダの作成に失敗"

#: notebook/static/tree/js/notebooklist.js:144
msgid "An error occurred while creating a new folder."
msgstr "新しいフォルダの作成中にエラーが発生しました。"

#: notebook/static/tree/js/notebooklist.js:271
msgid "Failed to read file"
msgstr "ファイルの読み込みに失敗しました"

#: notebook/static/tree/js/notebooklist.js:272
#, python-format
msgid "Failed to read file %s"
msgstr "%s の読み込みに失敗しました"

#: notebook/static/tree/js/notebooklist.js:283
#, python-format
msgid "The file size is %d MB. Do you still want to upload it?"
msgstr "ファイルサイズは %d MB です。本当にアップロードしますか？"

#: notebook/static/tree/js/notebooklist.js:286
msgid "Large file size warning"
msgstr "大きいファイルサイズの警告"

#: notebook/static/tree/js/notebooklist.js:355
msgid "Server error: "
msgstr "サーバエラー:"

#: notebook/static/tree/js/notebooklist.js:390
msgid "The notebook list is empty."
msgstr "ノートブック一覧は空です。"

#: notebook/static/tree/js/notebooklist.js:463
msgid "Click here to rename, delete, etc."
msgstr "リネーム、削除、その他を実行するにはここをクリック"

#: notebook/static/tree/js/notebooklist.js:503
msgid "Running"
msgstr "実行中"

#: notebook/static/tree/js/notebooklist.js:835
msgid "Enter a new file name:"
msgstr "新しいファイル名を入力:"

#: notebook/static/tree/js/notebooklist.js:836
msgid "Enter a new directory name:"
msgstr "新しいディレクトリ名を入力:"

#: notebook/static/tree/js/notebooklist.js:838
msgid "Enter a new name:"
msgstr "新しい名前を入力:"

#: notebook/static/tree/js/notebooklist.js:843
msgid "Rename file"
msgstr "ファイルのリネーム"

#: notebook/static/tree/js/notebooklist.js:844
msgid "Rename directory"
msgstr "ディレクトリのリネーム"

#: notebook/static/tree/js/notebooklist.js:845
msgid "Rename notebook"
msgstr "ノートブックのリネーム"

#: notebook/static/tree/js/notebooklist.js:859
msgid "Move"
msgstr "移動"

#: notebook/static/tree/js/notebooklist.js:875
msgid "An error occurred while renaming \"%1$s\" to \"%2$s\"."
msgstr "\"%1$s\" を \"%2$s\" にリネーム中にエラーが発生しました。"

#: notebook/static/tree/js/notebooklist.js:878
msgid "Rename Failed"
msgstr "リネームの失敗"

#: notebook/static/tree/js/notebooklist.js:927
#, python-format
msgid "Enter a new destination directory path for this item:"
msgid_plural "Enter a new destination directory path for these %d items:"
msgstr[0] "このアイテムの移動先を入力して下さい:"
msgstr[1] "%d 個のアイテムの移動先を入力して下さい:"

#: notebook/static/tree/js/notebooklist.js:940
#, python-format
msgid "Move an Item"
msgid_plural "Move %d Items"
msgstr[0] "アイテムの移動"
msgstr[1] "%d 個のアイテムの移動"

#: notebook/static/tree/js/notebooklist.js:959
msgid "An error occurred while moving \"%1$s\" from \"%2$s\" to \"%3$s\"."
msgstr "\"%1$s\" を \"%2$s\" から \"%3$s\" に移動する際に失敗しました。"

#: notebook/static/tree/js/notebooklist.js:961
msgid "Move Failed"
msgstr "ファイルの移動に失敗"

#: notebook/static/tree/js/notebooklist.js:1007
#, python-format
msgid "Are you sure you want to permanently delete: \"%s\"?"
msgid_plural "Are you sure you want to permanently delete the %d files or folders selected?"
msgstr[0] "本当に \"%s\" を完全に削除しますか？"
msgstr[1] "本当に %d 個のファイルまたはディレクトリを完全に削除しますか？"

#: notebook/static/tree/js/notebooklist.js:1035
#, python-format
msgid "An error occurred while deleting \"%s\"."
msgstr "\"%s\" を削除中にエラーが発生しました。"

#: notebook/static/tree/js/notebooklist.js:1037
msgid "Delete Failed"
msgstr "削除に失敗"

#: notebook/static/tree/js/notebooklist.js:1078
#, python-format
msgid "Are you sure you want to duplicate: \"%s\"?"
msgid_plural "Are you sure you want to duplicate the %d files selected?"
msgstr[0] "本当に \"%s\" を複製しますか？"
msgstr[1] "本当に %d 個のファイルを複製しますか？"

#: notebook/static/tree/js/notebooklist.js:1088
msgid "Duplicate"
msgstr "複製"

#: notebook/static/tree/js/notebooklist.js:1102
#, python-format
msgid "An error occurred while duplicating \"%s\"."
msgstr "\"%s\" の複製中に失敗"

#: notebook/static/tree/js/notebooklist.js:1104
msgid "Duplicate Failed"
msgstr "複製に失敗"

#: notebook/static/tree/js/notebooklist.js:1323
msgid "Upload"
msgstr "アップロード"

#: notebook/static/tree/js/notebooklist.js:1332
msgid "Invalid file name"
msgstr "ファイル名が不正です"

#: notebook/static/tree/js/notebooklist.js:1333
msgid "File names must be at least one character and not start with a period"
msgstr "ファイル名はピリオドで始まっていない1文字以上の名前でなければなりません"

#: notebook/static/tree/js/notebooklist.js:1362
msgid "Cannot upload invalid Notebook"
msgstr "不正なノートブックのためアップロードできません"

#: notebook/static/tree/js/notebooklist.js:1395
#, python-format
msgid "There is already a file named \"%s\". Do you want to replace it?"
msgstr "既に \"%s\" という名前のファイルが存在します。入れ替えますか？"

#: notebook/static/tree/js/notebooklist.js:1397
msgid "Replace file"
msgstr "ファイルの入れ替え"
