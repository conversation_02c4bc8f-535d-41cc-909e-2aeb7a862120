"""
Test cases for calculated field history functionality.
Tests that calculated fields only appear in history for source sheets, not derived sheets.
"""

import pytest
from django.test import TestCase
from django.utils import timezone
from uuid import uuid4

from everstage_ddd.datasheet.models import DatasheetVariable
from everstage_ddd.datasheet.services.datasheet_service import get_sheet_timeline
from spm.models import VariableDataType


@pytest.mark.django_db
class TestCalculatedFieldHistory(TestCase):
    """Test calculated field history functionality"""

    def setUp(self):
        """Set up test data"""
        self.client_id = 1001
        self.databook_id = str(uuid4())
        self.source_sheet_id = str(uuid4())
        self.derived_sheet_id = str(uuid4())
        
        # Create a data type for calculated fields
        self.data_type = VariableDataType.objects.create(
            data_type_id=1,
            data_type_name="Number",
            client_id=self.client_id
        )

    def test_calculated_field_appears_in_source_sheet_history(self):
        """Test that calculated fields appear in history for source sheets"""
        # Create a calculated field in source sheet (source_variable_id = None)
        calc_field = DatasheetVariable.objects.create(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.source_sheet_id,
            system_name="cf_test_field",
            display_name="Test Calculated Field",
            data_type=self.data_type,
            source_variable_id=None,  # This indicates it's a source calculated field
            source_id=str(self.source_sheet_id),
            source_type="datasheet",
            is_selected=True,
            field_order=1,
            meta_data={
                "ast": {"type": "add", "left": "field1", "right": "field2"},
                "infix": "field1 + field2",
                "criteria_type": "expression"
            },
            knowledge_begin_date=timezone.now()
        )

        # Get timeline for source sheet
        timeline = get_sheet_timeline(
            sheet_id=self.source_sheet_id,
            databook_id=self.databook_id,
            client_id=self.client_id
        )

        # Check that calculated field appears in timeline
        calc_field_events = [
            event for event in timeline 
            if event["action_type"] in ["calculated_field_added", "calculated_field_selected"]
        ]
        
        self.assertGreater(len(calc_field_events), 0, "Calculated field should appear in source sheet timeline")
        
        # Verify the calculated field event details
        calc_field_added_event = next(
            (event for event in calc_field_events if event["action_type"] == "calculated_field_added"),
            None
        )
        self.assertIsNotNone(calc_field_added_event, "calculated_field_added event should exist")
        self.assertEqual(calc_field_added_event["details"]["name"], "Test Calculated Field")

    def test_calculated_field_does_not_appear_in_derived_sheet_history(self):
        """Test that calculated fields do not appear in history for derived sheets"""
        # Create a calculated field in derived sheet (source_variable_id points to source sheet)
        source_calc_field_id = str(uuid4())
        
        calc_field = DatasheetVariable.objects.create(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.derived_sheet_id,
            system_name="cf_test_field",
            display_name="Test Calculated Field",
            data_type=self.data_type,
            source_variable_id=source_calc_field_id,  # This indicates it's derived from source sheet
            source_id=str(self.source_sheet_id),
            source_type="datasheet",
            is_selected=True,
            field_order=1,
            meta_data={
                "ast": {"type": "add", "left": "field1", "right": "field2"},
                "infix": "field1 + field2",
                "criteria_type": "expression"
            },
            knowledge_begin_date=timezone.now()
        )

        # Get timeline for derived sheet
        timeline = get_sheet_timeline(
            sheet_id=self.derived_sheet_id,
            databook_id=self.databook_id,
            client_id=self.client_id
        )

        # Check that calculated field does NOT appear in timeline
        calc_field_events = [
            event for event in timeline 
            if event["action_type"] in ["calculated_field_added", "calculated_field_selected"]
        ]
        
        self.assertEqual(len(calc_field_events), 0, "Calculated field should NOT appear in derived sheet timeline")

    def test_regular_variables_not_affected(self):
        """Test that regular (non-calculated) variables are not affected by the filter"""
        # Create a regular variable (not a calculated field)
        regular_field = DatasheetVariable.objects.create(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.source_sheet_id,
            system_name="regular_field",
            display_name="Regular Field",
            data_type=self.data_type,
            source_variable_id=None,
            source_id="custom_object_123",
            source_type="custom_object",
            is_selected=True,
            field_order=0,  # Regular fields have field_order = 0
            meta_data=None,  # Regular fields don't have calculation metadata
            knowledge_begin_date=timezone.now()
        )

        # Get timeline for source sheet
        timeline = get_sheet_timeline(
            sheet_id=self.source_sheet_id,
            databook_id=self.databook_id,
            client_id=self.client_id
        )

        # Regular variables should not appear in timeline since they're not calculated fields
        # The timeline only shows calculated fields for variables
        calc_field_events = [
            event for event in timeline 
            if event["action_type"] in ["calculated_field_added", "calculated_field_selected"]
        ]
        
        # Should be empty since regular_field is not a calculated field
        self.assertEqual(len(calc_field_events), 0, "Regular fields should not appear in calculated field timeline")

    def test_multiple_calculated_fields_source_vs_derived(self):
        """Test multiple calculated fields - some in source, some in derived sheets"""
        # Create calculated field in source sheet
        source_calc_field = DatasheetVariable.objects.create(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.source_sheet_id,
            system_name="cf_source_field",
            display_name="Source Calculated Field",
            data_type=self.data_type,
            source_variable_id=None,  # Source field
            source_id=str(self.source_sheet_id),
            source_type="datasheet",
            is_selected=True,
            field_order=1,
            meta_data={
                "ast": {"type": "add", "left": "field1", "right": "field2"},
                "infix": "field1 + field2",
                "criteria_type": "expression"
            },
            knowledge_begin_date=timezone.now()
        )

        # Create calculated field in derived sheet
        derived_calc_field = DatasheetVariable.objects.create(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.derived_sheet_id,
            system_name="cf_source_field",  # Same field name but derived
            display_name="Source Calculated Field",
            data_type=self.data_type,
            source_variable_id=str(source_calc_field.variable_id),  # Points to source
            source_id=str(self.source_sheet_id),
            source_type="datasheet",
            is_selected=True,
            field_order=1,
            meta_data={
                "ast": {"type": "add", "left": "field1", "right": "field2"},
                "infix": "field1 + field2",
                "criteria_type": "expression"
            },
            knowledge_begin_date=timezone.now()
        )

        # Get timeline for source sheet
        source_timeline = get_sheet_timeline(
            sheet_id=self.source_sheet_id,
            databook_id=self.databook_id,
            client_id=self.client_id
        )

        # Get timeline for derived sheet
        derived_timeline = get_sheet_timeline(
            sheet_id=self.derived_sheet_id,
            databook_id=self.databook_id,
            client_id=self.client_id
        )

        # Source sheet should have calculated field events
        source_calc_events = [
            event for event in source_timeline 
            if event["action_type"] in ["calculated_field_added", "calculated_field_selected"]
        ]
        self.assertGreater(len(source_calc_events), 0, "Source sheet should have calculated field events")

        # Derived sheet should NOT have calculated field events
        derived_calc_events = [
            event for event in derived_timeline 
            if event["action_type"] in ["calculated_field_added", "calculated_field_selected"]
        ]
        self.assertEqual(len(derived_calc_events), 0, "Derived sheet should NOT have calculated field events")
