import { EditPencilIcon } from "@everstage/evericons/outlined";
import { observer } from "mobx-react";
import React, { useContext, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useQuery, useMutation } from "react-query";

import { DATASHEET_VIEW_BY, RBAC_ROLES } from "~/Enums";
import { useSupabasePresence } from "~/everstage-supabase";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverHotToastAlert,
  EverHotToastNotification,
  toast,
  EverButton,
  EverHotToastMessage,
} from "~/v2/components";

import { DatasheetName } from "./DatasheetName";
import { MoreButton } from "./MoreButton";
import { DatasheetContext } from "../../DatasheetStore";
import { UsersList } from "../../helperComponents/UsersList";
import useFetchApiWithAuth from "../../useFetchApiWithAuth";
import { VersionToggleButton } from "../VersionToggleButton";
//import { updateQueryParam } from "../../utils";

export const HeaderPanel = observer(
  ({
    onUpdateDatasheet,
    showGenerateSheetModal,
    setIsDatasheetNotAvailable,
    setObjectRestrictedMessage,
    hasFlushApiAttemptedRef,
    setShowGenerateSheetModal,
    activeKey,
    sendMessage,
    pivotConfig,
    isRefreshRequestLoading,
    refetchForceSkippedDatasheetIds,
    isTimelineModalVisible,
    setTimelineModalVisible,
  }) => {
    const { t } = useTranslation();
    const { email, accessToken } = useAuthStore();
    const { firstName, lastName, profilePicture } = useEmployeeStore();
    const datasheetStore = useContext(DatasheetContext);
    const { hasPermissions } = useUserPermissionStore();
    const { fetchData } = useFetchApiWithAuth();

    const { refetch, isLoading, isRefetching } = useQuery(
      ["getDatasheetDetails", datasheetStore.datasheetId],
      () => {
        return fetchData(
          `/datasheets/${datasheetStore.datasheetId}/details/canvas`,
          "GET"
        );
      },
      {
        cacheTime: 0,
        refetchOnWindowFocus: false,
        retry: false,
        onError: (error) => {
          if (
            error
              ?.toString()
              ?.includes("Datasheet matching query does not exist")
          ) {
            setIsDatasheetNotAvailable(true);
            return;
          }

          if (
            error
              ?.toString()
              ?.includes(
                "You don't have permission to view the contents of this sheet"
              )
          ) {
            setIsDatasheetNotAvailable(true);
            setObjectRestrictedMessage(
              error?.toString().replace(/^Error:\s*/, "")
            );
            return;
          }

          datasheetStore.setShowErrorPage(true);
          toast.custom(
            (t) => (
              <EverHotToastNotification
                type="error"
                title="Loading datasheet failed."
                description={"Please contact your admin."}
                toastId={t.id}
              />
            ),
            { position: "top-right", duration: 5000 }
          );

          //close edit drawer when datasheet is not available
          datasheetStore.setEditDrawerVisiblity(false);
        },
        onSettled: (data, error) => {
          if (error) return console.log("Error occurred:", error);
          datasheetStore.setDatasheetDetails(data);
          datasheetStore.setDatabookId(data?.databook_id);

          // Only update recent sheets if not viewing archived databooks
          if (datasheetStore.viewBy === DATASHEET_VIEW_BY.ARCHIVED_DATABOOKS) {
            return;
          }

          // Retrieve recent datasheets from localStorage or initialize as an empty array if not available
          const jsonRecentDatasheets =
            localStorage.getItem("recent_datasheets") || "[]";
          // Parse recent datasheets into an array
          const parsedRecentDatasheets = JSON.parse(jsonRecentDatasheets);
          // Filter out the current item from the recent datasheets
          const filteredSheets = parsedRecentDatasheets.filter((datasheet) => {
            // Return true if the datasheet_id of the current item does not match with the datasheet_id of the item in the recent datasheets
            return datasheet?.datasheet_id !== data.datasheet_id;
          });
          // Add the current item to the beginning of the filtered datasheets array
          filteredSheets.unshift({
            datasheet_id: data.datasheet_id,
            name: data.name,
          });
          // Limit the number of recent datasheets to 3 and update in localStorage
          localStorage.setItem(
            "recent_datasheets",
            JSON.stringify(filteredSheets.slice(0, 3))
          );

          // Update the recent sheets state in datasheetStore
          datasheetStore.setRecentSheets(filteredSheets.slice(0, 3));
        },
      }
    );

    const handleDiscard = useMutation(() => {
      return fetchData(
        `datasheet_ninja/${datasheetStore.datasheetId}/flush-draft-details`,
        "POST"
      );
    });

    useEffect(() => {
      datasheetStore.setDatasheetDetailsIsLoading(isLoading);
    }, [isLoading]);

    useEffect(() => {
      datasheetStore.setIsDatasheetDetailsRefetching(isRefetching);
    }, [isRefetching]);

    const trackingObj = {
      email: email,
      profilePicture: profilePicture,
      name: `${firstName} ${lastName}`,
      online_at: new Date().toISOString(),
      context: "view",
    };
    const event = ["sync"];
    const presenceConfig = { key: datasheetStore.datasheetId };
    const params = {
      channelPrefix: "online-users-datasheet",
      presenceConfig,
      trackingObj,
    };

    const presence = useSupabasePresence(event, params);

    async function handleUpdateDatasheet(toastId, isForceInvalidate = false) {
      toastId && toast.dismiss(toastId);
      onUpdateDatasheet(isForceInvalidate);
    }

    useEffect(() => {
      const arr = [];
      if (presence?.presenceType === "sync") {
        let users = presence?.data[datasheetStore.datasheetId] || [];
        for (const user of users) {
          user.context === "edit" &&
            user.email != email &&
            arr.push({
              email: user.email,
              profilePicture: user.profilePicture,
              name: user.name,
            });
        }
        // If no active users are found and the flush API hasn't been attempted yet
        if (arr.length === 0 && !hasFlushApiAttemptedRef.current) {
          handleDiscard.mutate(); // Call the flush API
        }
        // Mark that the flush API has been attempted
        hasFlushApiAttemptedRef.current = true;
        datasheetStore.setActiveUsers(arr);
      }
    }, [presence]);

    useEffect(() => {
      datasheetStore.setRefetchDatasheetDetails(refetch);
    }, []);

    useEffect(() => {
      toast.dismiss();
      return () => toast.dismiss();
    }, [datasheetStore.datasheetId]);
    return (
      <div className="flex justify-between w-full h-14">
        {/* TODO: Merge these conditions */}
        {datasheetStore.isSyncInProgress &&
          !showGenerateSheetModal &&
          !isRefetching && <LoadingBanner />}
        {!datasheetStore.datasheetDetails?.is_archived &&
          !datasheetStore.isDatabookSyncInProgress &&
          !showGenerateSheetModal &&
          !isRefetching &&
          !datasheetStore.isSyncInProgress &&
          datasheetStore?.datasheetDetails?.is_stale &&
          datasheetStore?.datasheetDetails?.is_datasheet_generated &&
          !datasheetStore?.datasheetDetails?.is_calc_field_changed &&
          !datasheetStore?.datasheetDetails?.is_config_changed &&
          !datasheetStore?.datasheetDetails?.is_active_sync_present &&
          !datasheetStore?.datasheetDetailsIsLoading && (
            <StaleBanner
              t={t}
              handleUpdateDatasheet={handleUpdateDatasheet}
              hasPermissions={hasPermissions}
              isRefreshRequestLoading={isRefreshRequestLoading}
            />
          )}
        <DatasheetName
          t={t}
          isTimelineModalVisible={isTimelineModalVisible}
          setTimelineModalVisible={setTimelineModalVisible}
        />
        <div className="flex gap-2 items-start">
          <UsersList />
          <div className="flex items-center">
            <VersionToggleButton showDivider={true} sendMessage={sendMessage} />
          </div>
          {!datasheetStore.datasheetDetailsIsLoading && (
            <>
              {hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) &&
                !datasheetStore.datasheetDetails?.is_archived && (
                  <EverButton
                    type="filled"
                    color="base"
                    prependIcon={<EditPencilIcon />}
                    size="small"
                    onClick={() => {
                      const url = new URL(window.location);
                      const searchParams = new URLSearchParams(url.search);
                      // Set or update the query parameter with the provided key and value
                      searchParams.set("isEditView", true);
                      // Push the updated search parameters to the browser history, updating the URL
                      // history.push({ search: searchParams.toString() });
                      searchParams.delete("adjustmentId");
                      searchParams.delete("rowKey");
                      url.search = searchParams.toString();
                      // Use history.replaceState to update the URL without reloading the page
                      window.history.replaceState({}, "", url);

                      datasheetStore.setEditDrawerVisiblity(true);
                    }}
                    disabled={datasheetStore.isSyncInProgress}
                  >
                    Edit
                  </EverButton>
                )}
              <MoreButton
                handleUpdateDatasheet={handleUpdateDatasheet}
                accessToken={accessToken}
                showGenerateSheetModal={showGenerateSheetModal}
                setShowGenerateSheetModal={setShowGenerateSheetModal}
                activeKey={activeKey}
                viewBy={datasheetStore.viewBy}
                pivotConfig={pivotConfig}
                refetchForceSkippedDatasheetIds={
                  refetchForceSkippedDatasheetIds
                }
              />
            </>
          )}
        </div>
      </div>
    );
  }
);

function LoadingBanner() {
  return (
    <div className="absolute left-1/2 transform -translate-x-[375px] z-10">
      <EverHotToastNotification
        type="loading"
        title="Fetching latest data..."
        description="This process may take some time to complete"
        showClose={false}
      />
    </div>
  );
}

function StaleBanner({
  t,
  handleUpdateDatasheet,
  hasPermissions,
  isRefreshRequestLoading,
}) {
  return (
    <div className="absolute left-1/2 transform -translate-x-[375px] z-10">
      {hasPermissions(
        [RBAC_ROLES.MANAGE_DATABOOK, RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS],
        false
      ) ? (
        <EverHotToastAlert
          type="warning"
          title="Sheet has new updates"
          description="Refresh sheet to load latest data"
          id={"applyNewChanges"}
          buttons={[
            {
              buttonText: "Refresh",
              onClick: () => handleUpdateDatasheet(t.id),
              className: "border-l",
              disabled: isRefreshRequestLoading,
            },
          ]}
        />
      ) : (
        <EverHotToastMessage
          type="warning"
          description={
            "Sheet has new updates. Please ask your administrator to update the changes."
          }
        />
      )}
    </div>
  );
}
