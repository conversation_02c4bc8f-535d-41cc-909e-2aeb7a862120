import { LogInIcon } from "@everstage/evericons/duotone";
import { ChevronLeftIcon } from "@everstage/evericons/outlined";
import { useLocalStore, observer } from "mobx-react";
import React, { useState, useEffect, useRef } from "react";
import { Helmet } from "react-helmet";
import { useQuery as useReactQuery } from "react-query";
import { useLocation } from "react-router-dom";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverLoader } from "~/v2/components";
import { EverDrawer } from "~/v2/components";
import SheetTimeline from "~/v2/components/SheetTimeline";

import { DataCanvasView } from "./data-canvas-view";
import DatasheetStore, { DatasheetContext } from "./DatasheetStore";
import { EditView } from "./edit-view";
import { ExplorerView } from "./explorer-view/ExplorerView";
import { CreateSheetModal } from "./helperComponents/CreateSheetModal";
import { DatabookGraph } from "./helperComponents/DatabookGraph";
import { DatasheetDetails } from "./helperComponents/DatasheetDetailsModal";
import { DeleteSheetModal } from "./helperComponents/DeleteSheetModal";
import { VersionChangeModal } from "./helperComponents/VersionChangeModal";
import { NoDatasheetPlaceholder } from "./NoDatasheetPlaceholder";
import { getForceSkippedDatasheetIds } from "./restApi";

const Datasheet = observer(({ showVersionChangeBanner, sendMessage }) => {
  const datasheetStore = useLocalStore(() => new DatasheetStore());
  // Using the v1 global databook store to refetch databooks, as canvas relies on this list.
  // The refetchDatabooks function makes a GraphQL call to retrieve all databooks with their details.
  // TODO: Find an alternative solution to avoid using refetch from the v1 store.
  const { refetchDatabooks } = useDatabookStore();
  const { isLoggedInAsUser, isSupportUser, accessToken } = useAuthStore();
  const location = useLocation();
  const hasFlushApiAttemptedRef = useRef(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isChevronHovered, setIsChevronHovered] = useState(false);
  const [showGenerateSheetModal, setShowGenerateSheetModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const collapsedClassNames = "w-12 bg-ever-base-50 cursor-pointer";
  const collapsedHoverClassNames = "w-12 bg-ever-base-200 cursor-pointer";
  const expandClassNames = `w-[16.5vw] ${
    isChevronHovered &&
    "hover:border-r-2 hover:border-r-ever-primary duration-0"
  } `;
  const { hasPermissions } = useUserPermissionStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const [isTimelineModalVisible, setTimelineModalVisible] = useState(false);

  const isNewSidebar =
    clientFeatures?.enableSidebarV3 ||
    localStorage.getItem("new-sidebar") === "true";

  useEffect(() => {
    // Get the query string from the URL
    const queryString = location.search;
    // Parse the query string
    const urlParams = new URLSearchParams(queryString);
    // Get the value of a isEditView
    if (urlParams.get("isEditView")) {
      if (hasPermissions(RBAC_ROLES.MANAGE_DATABOOK)) {
        datasheetStore.setEditDrawerVisiblity(true);
      } else {
        const url = new URL(window.location);
        const searchParams = new URLSearchParams(url.search);
        searchParams.delete("isEditView");
        url.search = searchParams.toString();
        // Use history.replaceState to update the URL without reloading the page
        window.history.replaceState({}, "", url);
      }
    }
    if (urlParams.get("detailsDatasheetId")) {
      datasheetStore.setDrawerVisibility(true);
      datasheetStore.setDetailsDatasheetId(urlParams.get("detailsDatasheetId"));
    }
    // Setting the refetch function from the global store into the local datasheet store
    // By this we wont have to call useDatabookStore at multiple places
    // Also, When the solutioning for this is done, the refetch function can be updated here rather going to multiple files and updating
    datasheetStore.setGlobalDatabookRefetch(refetchDatabooks);
  }, []);

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);

    if (urlParams.get("id")) {
      datasheetStore.setDatasheetId(urlParams.get("id"));
      datasheetStore.setCurrentDatasheetName(urlParams.get("name"));
    }
  }, [location.search]);

  // Fetch the force-skipped datasheet IDs when the user is navigated to the datasheet page
  // and store it in the datasheet store.
  const { refetch: refetchForceSkippedDatasheetIds } = useReactQuery(
    ["forceSkippedDatasheetIds", accessToken],
    () => getForceSkippedDatasheetIds(accessToken),
    {
      enabled: !!accessToken,
      onSuccess: (data) => {
        datasheetStore.setForceSkippedDatasheetIds(
          data?.skipped_datasheet_ids || []
        );
      },
      onError: () => {
        datasheetStore.setForceSkippedDatasheetIds([]);
      },
    }
  );

  if (showVersionChangeBanner) {
    return (
      <div
        className="w-full h-full relative"
        id="version-change-modal-container"
      >
        <VersionChangeModal
          databookId={datasheetStore.datasheetDetails?.databook_id}
          visible={showVersionChangeBanner}
        />
      </div>
    );
  }

  let dSHeight = "h-screen";
  if (isNewSidebar) {
    if (isLoggedInAsUser || isSupportUser) {
      dSHeight = "h-[calc(100vh-84px)]";
    } else {
      dSHeight = "h-[calc(100vh-44px)]";
    }
    if (isLoggedInAsUser && isSupportUser) {
      dSHeight = "h-[calc(100vh-124px)]";
    }
  }

  return (
    <DatasheetContext.Provider value={datasheetStore}>
      {
        <>
          <Helmet>
            <title>{`${
              datasheetStore?.currentDatasheetName
                ? `${datasheetStore.currentDatasheetName} - Datasheet`
                : "Datasheet"
            }`}</title>
          </Helmet>
          <div className="w-full h-full flex">
            <EverDrawer
              visible={isTimelineModalVisible}
              onClose={() => setTimelineModalVisible(false)}
              placement="right"
              width="80vw"
              footer={null}
              title={
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">S</span>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-gray-900">
                      Sheet History
                    </div>
                    <div className="text-sm text-gray-500">
                      View and track all changes made to your sheet
                    </div>
                  </div>
                </div>
              }
            >
              <SheetTimeline
                datasheetId={datasheetStore.datasheetId}
                databookId={datasheetStore.databookId}
                isVisible={isTimelineModalVisible}
              />
            </EverDrawer>
            <div
              className={twMerge(
                "flex flex-col border border-solid border-ever-base-400 border-l-0 border-t-0 border-b-0 ease-in-out duration-500 relative",
                isCollapsed ? collapsedClassNames : expandClassNames,
                isCollapsed && isHovered && collapsedHoverClassNames,
                dSHeight
              )}
              onClick={() => {
                if (isCollapsed) {
                  setIsCollapsed(!isCollapsed);
                }
              }}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <div
                className={`border border-solid border-transparent z-50 hover:bg-ever-base-100 absolute -right-[12px] top-6 rounded-full w-6 h-6 flex items-center justify-center ease-in-out duration-500 bg-ever-base-25 cursor-pointer shadow-md  ${
                  isCollapsed ? "hidden" : "flex"
                }
              }`}
                onClick={() => setIsCollapsed(!isCollapsed)}
                onMouseEnter={() => {
                  setIsChevronHovered(true);
                }}
                onMouseLeave={() => {
                  setIsChevronHovered(false);
                }}
              >
                <ChevronLeftIcon className="w-4 h-4 text-ever-base-content" />
              </div>
              {isCollapsed ? (
                <div className="flex mt-4 w-full justify-center cursor-pointer ">
                  <LogInIcon
                    className={twMerge(
                      "w-6 h-6 ease-in-out duration-500",
                      isCollapsed && isHovered
                        ? "text-ever-base-content"
                        : "text-ever-base-content-mid"
                    )}
                  />
                </div>
              ) : (
                <ExplorerView
                  refetchForceSkippedDatasheetIds={
                    refetchForceSkippedDatasheetIds
                  }
                />
              )}
            </div>
            <div
              className={twMerge(
                "flex flex-col flex-auto px-4 pt-2.5 gap-1 relative grow",
                localStorage.getItem("isSidebarPinned") === "true"
                  ? "w-[calc(83vw-214px)]"
                  : "w-[calc(83vw-70px)]",
                dSHeight
              )}
              id="datasheet-canvas-view"
            >
              {!datasheetStore.isDatasheetIdSynced &&
              !datasheetStore.datasheetId ? (
                <div className="flex items-center justify-center h-full w-full">
                  <EverLoader.SpinnerLottie className="w-20 h-20" />
                </div>
              ) : datasheetStore.datasheetId ? (
                <DataCanvasView
                  setShowGenerateSheetModal={setShowGenerateSheetModal}
                  showGenerateSheetModal={showGenerateSheetModal}
                  hasFlushApiAttemptedRef={hasFlushApiAttemptedRef}
                  sendMessage={sendMessage}
                  refetchForceSkippedDatasheetIds={
                    refetchForceSkippedDatasheetIds
                  }
                  isTimelineModalVisible={isTimelineModalVisible}
                  setTimelineModalVisible={setTimelineModalVisible}
                />
              ) : (
                <NoDatasheetPlaceholder />
              )}
            </div>
            {datasheetStore.drawerVisibility && <DatasheetDetails />}
            {datasheetStore.showGraph && <DatabookGraph />}
            {datasheetStore.editDrawerVisiblity && (
              <EditView setShowGenerateSheetModal={setShowGenerateSheetModal} />
            )}
            {datasheetStore.createModalVisible && <CreateSheetModal />}
            <DeleteSheetModal />
          </div>
        </>
      }
    </DatasheetContext.Provider>
  );
});

export default Datasheet;
